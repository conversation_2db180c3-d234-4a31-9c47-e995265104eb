[2025-05-30 09:38:37] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"JltC4UNJVZCX0D2wr6kk3brcfphElpYtyn4EYlec","status":"Pending Approval","changed_status":"Sent","displayed_ids":["657"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["657"]} 
[2025-05-30 09:38:37] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-05-30 09:38:37] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-05-30 09:38:37] local.INFO: Filtering displayed_ids {"original_count":1,"filtered_count":1,"original_ids":["657"],"filtered_ids":["657"]} 
[2025-05-30 09:38:38] local.INFO: Using filtered displayed_ids {"count":1,"results_count":1} 
[2025-05-30 09:38:38] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_ids":[657],"count":1} 
[2025-05-30 09:38:38] local.INFO: SendFilesToFaxJob dispatched successfully  
[2025-05-30 09:38:40] local.INFO: SendFilesToFaxJob starting {"import_file_ids":[657],"count":1} 
[2025-05-30 09:38:40] local.INFO: Files retrieved for processing {"count":1,"file_ids":[657]} 
[2025-05-30 09:38:40] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 09:38:40] local.INFO: Merging multiple PDFs {"file_count":1} 
[2025-05-30 09:38:44] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:08:43 +0630","user_id":1} 
[2025-05-30 09:38:45] local.INFO: Successfully merged and uploaded PDFs {"merged_count":1,"merged_file":"temp/fax_merged_pdfs/upSX5u4sDOq96stjuZET.pdf"} 
[2025-05-30 09:38:46] local.INFO: Fax numbers retrieved with + prefix {"count":1,"numbers":["+18885110528"]} 
[2025-05-30 09:38:47] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 09:38:47] local.INFO: Fax sent successfully {"to":["+18885110528"],"from":"+18669938841","files":["/storage/transient-6921bd4990ca4eee8da3c399e7ce7f9e.pdf"]} 
[2025-05-30 09:38:47] local.INFO: Cleaned up temporary PDF file {"file":"temp/fax_merged_pdfs/upSX5u4sDOq96stjuZET.pdf"} 
[2025-05-30 09:38:47] local.INFO: Removed empty temporary directory {"directory":"temp/fax_merged_pdfs"} 
[2025-05-30 09:57:06] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:27:05 +0630","user_id":1} 
[2025-05-30 10:00:37] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"JltC4UNJVZCX0D2wr6kk3brcfphElpYtyn4EYlec","status":"Pending Approval","changed_status":"Sent"},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":null} 
[2025-05-30 10:00:37] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-05-30 10:00:37] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-05-30 10:00:37] local.INFO: No displayed_ids provided, using all matching records {"results_count":6} 
[2025-05-30 10:00:37] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_ids":[658,659,660,661,662,663],"count":6} 
[2025-05-30 10:00:37] local.INFO: SendFilesToFaxJob dispatched successfully  
[2025-05-30 10:00:40] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:30:39 +0630","user_id":1} 
[2025-05-30 10:00:41] local.INFO: SendFilesToFaxJob starting {"import_file_ids":[658,659,660,661,662,663],"count":6} 
[2025-05-30 10:00:41] local.INFO: Files retrieved for processing {"count":6,"file_ids":[658,659,660,661,662,663]} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Merging multiple PDFs {"file_count":6} 
[2025-05-30 10:00:45] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:30:45 +0630","user_id":1} 
[2025-05-30 10:00:49] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:30:48 +0630","user_id":1} 
[2025-05-30 10:01:05] local.INFO: Successfully merged and uploaded PDFs {"merged_count":6,"merged_file":"temp/fax_merged_pdfs/uTqRilI7iJcKF2xcYY2O.pdf"} 
[2025-05-30 10:01:05] local.INFO: Fax numbers retrieved with + prefix {"count":1,"numbers":["+18885110528"]} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Fax sent successfully {"to":["+18885110528"],"from":"+18669938841","files":["/storage/transient-5fd192d7fbd44ab883cb06af10430db8.pdf"]} 
[2025-05-30 10:01:06] local.INFO: Cleaned up temporary PDF file {"file":"temp/fax_merged_pdfs/uTqRilI7iJcKF2xcYY2O.pdf"} 
[2025-05-30 10:01:06] local.INFO: Removed empty temporary directory {"directory":"temp/fax_merged_pdfs"} 
[2025-06-02 04:44:06] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:14:04 +0630","user_id":1} 
[2025-06-02 04:48:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:18:08 +0630","user_id":1} 
[2025-06-02 04:49:33] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:19:33 +0630","user_id":1} 
[2025-06-02 04:49:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:19:59 +0630","user_id":1} 
[2025-06-02 04:50:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:20:19 +0630","user_id":1} 
[2025-06-02 04:50:33] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:20:32 +0630","user_id":1} 
[2025-06-02 04:53:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:23:27 +0630","user_id":1} 
[2025-06-02 04:53:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:23:37 +0630","user_id":1} 
[2025-06-02 04:53:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:23:47 +0630","user_id":1} 
[2025-06-02 04:57:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:07 +0630","user_id":1} 
[2025-06-02 04:57:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:39 +0630","user_id":1} 
[2025-06-02 04:57:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:50 +0630","user_id":1} 
[2025-06-02 04:57:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:56 +0630","user_id":1} 
[2025-06-02 04:58:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:08 +0630","user_id":1} 
[2025-06-02 04:58:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:19 +0630","user_id":1} 
[2025-06-02 04:58:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:37 +0630","user_id":1} 
[2025-06-02 04:58:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:52 +0630","user_id":1} 
[2025-06-02 04:59:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:06 +0630","user_id":1} 
[2025-06-02 04:59:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:10 +0630","user_id":1} 
[2025-06-02 04:59:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:24 +0630","user_id":1} 
[2025-06-02 04:59:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:51 +0630","user_id":1} 
[2025-06-02 05:00:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:59 +0630","user_id":1} 
[2025-06-02 05:00:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:30:07 +0630","user_id":1} 
[2025-06-02 05:00:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:30:40 +0630","user_id":1} 
[2025-06-02 05:01:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:00 +0630","user_id":1} 
[2025-06-02 05:01:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:18 +0630","user_id":1} 
[2025-06-02 05:01:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:20 +0630","user_id":1} 
[2025-06-02 05:01:42] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:42 +0630","user_id":1} 
[2025-06-02 05:01:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:49 +0630","user_id":1} 
[2025-06-02 05:01:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:55 +0630","user_id":1} 
[2025-06-02 05:02:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:32:26 +0630","user_id":1} 
[2025-06-02 05:03:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:33:02 +0630","user_id":1} 
[2025-06-02 05:03:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:33:41 +0630","user_id":1} 
[2025-06-02 05:03:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:33:49 +0630","user_id":1} 
[2025-06-02 05:07:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:37:57 +0630","user_id":1} 
[2025-06-02 05:08:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:38:36 +0630","user_id":1} 
[2025-06-02 05:08:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:38:52 +0630","user_id":1} 
[2025-06-02 05:09:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:39:07 +0630","user_id":1} 
[2025-06-02 05:09:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:39:33 +0630","user_id":1} 
[2025-06-02 05:10:02] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:40:01 +0630","user_id":1} 
[2025-06-02 05:10:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:40:15 +0630","user_id":1} 
[2025-06-02 05:15:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:45:32 +0630","user_id":1} 
[2025-06-02 05:16:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:46:21 +0630","user_id":1} 
[2025-06-02 05:16:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:46:23 +0630","user_id":1} 
[2025-06-02 05:20:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:50:45 +0630","user_id":1} 
[2025-06-02 05:21:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:51:03 +0630","user_id":1} 
[2025-06-02 05:21:26] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:51:25 +0630","user_id":1} 
[2025-06-02 05:38:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:08:44 +0630","user_id":1} 
[2025-06-02 05:39:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:09:08 +0630","user_id":1} 
[2025-06-02 05:39:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:09:30 +0630","user_id":1} 
[2025-06-02 05:39:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:09:48 +0630","user_id":1} 
[2025-06-02 05:40:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:10:35 +0630","user_id":1} 
[2025-06-02 05:43:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:13:34 +0630","user_id":1} 
[2025-06-02 05:45:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:15:29 +0630","user_id":1} 
[2025-06-02 05:45:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:15:44 +0630","user_id":1} 
[2025-06-02 05:47:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:17:23 +0630","user_id":1} 
[2025-06-02 05:48:10] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:18:10 +0630","user_id":1} 
[2025-06-02 05:48:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:18:38 +0630","user_id":1} 
[2025-06-02 05:49:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:19:11 +0630","user_id":1} 
[2025-06-02 05:49:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:19:20 +0630","user_id":1} 
[2025-06-02 05:50:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:20:19 +0630","user_id":1} 
[2025-06-02 05:50:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:20:30 +0630","user_id":1} 
[2025-06-02 05:52:14] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:22:13 +0630","user_id":1} 
[2025-06-02 05:54:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:24:51 +0630","user_id":1} 
[2025-06-02 05:55:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:25:14 +0630","user_id":1} 
[2025-06-02 05:55:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:25:37 +0630","user_id":1} 
[2025-06-02 05:58:18] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:28:17 +0630","user_id":1} 
[2025-06-02 06:05:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:35:16 +0630","user_id":1} 
[2025-06-02 06:08:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:38:04 +0630","user_id":1} 
[2025-06-02 06:08:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:38:23 +0630","user_id":1} 
[2025-06-02 06:09:14] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:39:13 +0630","user_id":1} 
[2025-06-02 06:10:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:40:23 +0630","user_id":1} 
[2025-06-02 06:10:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:40:51 +0630","user_id":1} 
[2025-06-02 06:11:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:41:43 +0630","user_id":1} 
[2025-06-02 06:12:13] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:42:13 +0630","user_id":1} 
[2025-06-02 06:12:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:42:32 +0630","user_id":1} 
[2025-06-02 06:12:42] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:42:42 +0630","user_id":1} 
[2025-06-02 06:13:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:43:14 +0630","user_id":1} 
[2025-06-02 06:13:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:43:50 +0630","user_id":1} 
[2025-06-02 06:14:13] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:44:12 +0630","user_id":1} 
[2025-06-02 06:14:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:44:30 +0630","user_id":1} 
[2025-06-02 06:15:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:45:57 +0630","user_id":1} 
[2025-06-02 06:16:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:46:34 +0630","user_id":1} 
[2025-06-02 06:17:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:47:22 +0630","user_id":1} 
[2025-06-02 06:18:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:48:30 +0630","user_id":1} 
[2025-06-02 06:19:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:49:04 +0630","user_id":1} 
[2025-06-02 06:19:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:49:42 +0630","user_id":1} 
[2025-06-02 06:20:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:50:09 +0630","user_id":1} 
[2025-06-02 06:20:24] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:181)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(181): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(107): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:22:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:52:00 +0630","user_id":1} 
[2025-06-02 06:22:04] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:29:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:59:37 +0630","user_id":1} 
[2025-06-02 06:30:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:00:29 +0630","user_id":1} 
[2025-06-02 06:31:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:01:08 +0630","user_id":1} 
[2025-06-02 06:31:13] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:31:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:01:56 +0630","user_id":1} 
[2025-06-02 06:32:02] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:32:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:02:08 +0630","user_id":1} 
[2025-06-02 06:32:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:02:12 +0630","user_id":1} 
[2025-06-02 06:32:21] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:33:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:03:45 +0630","user_id":1} 
[2025-06-02 06:33:50] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:184)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(184): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:34:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:04:02 +0630","user_id":1} 
[2025-06-02 06:34:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:04:41 +0630","user_id":1} 
[2025-06-02 06:35:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:05:37 +0630","user_id":1} 
[2025-06-02 06:36:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:06:27 +0630","user_id":1} 
[2025-06-02 06:37:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:07:36 +0630","user_id":1} 
[2025-06-02 06:38:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:08:42 +0630","user_id":1} 
[2025-06-02 06:39:18] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:09:17 +0630","user_id":1} 
[2025-06-02 06:39:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:09:44 +0630","user_id":1} 
[2025-06-02 06:40:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:10:43 +0630","user_id":1} 
[2025-06-02 06:46:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:16:45 +0630","user_id":1} 
[2025-06-02 06:46:53] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"1234567890","label":"Test Fax"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 06:47:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:17:46 +0630","user_id":1} 
[2025-06-02 06:49:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:19:27 +0630","user_id":1} 
[2025-06-02 06:49:46] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"1234567890","label":"Test Fax"}],"newFaxInputs":[{"number":"43165468768","label":"new fax"}]} 
[2025-06-02 06:52:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:22:00 +0630","user_id":1} 
[2025-06-02 06:53:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:22:59 +0630","user_id":1} 
[2025-06-02 06:53:22] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"12345678905","label":"Test Fax"}],"newFaxInputs":[{"number":"14785125631","label":"new test"}]} 
[2025-06-02 06:53:40] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"12345678905","label":"Test Fax"}],"newFaxInputs":[{"number":"14785125631","label":"new test"}]} 
[2025-06-02 06:58:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:28:15 +0630","user_id":1} 
[2025-06-02 06:58:41] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"1234567890","label":"Test Fax"}],"newFaxInputs":[{"number":"125412563225","label":"new fax"}]} 
[2025-06-02 06:59:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:29:30 +0630","user_id":1} 
[2025-06-02 07:00:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:30:52 +0630","user_id":1} 
[2025-06-02 07:01:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:31:02 +0630","user_id":1} 
[2025-06-02 07:01:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:31:41 +0630","user_id":1} 
[2025-06-02 07:02:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:32:18 +0630","user_id":1} 
[2025-06-02 07:02:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:32:34 +0630","user_id":1} 
[2025-06-02 07:15:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:45:53 +0630","user_id":1} 
[2025-06-02 07:27:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:57:11 +0630","user_id":1} 
[2025-06-02 07:27:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:57:20 +0630","user_id":1} 
[2025-06-02 07:30:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 13:00:56 +0630","user_id":1} 
[2025-06-02 08:50:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:20:27 +0630","user_id":1} 
[2025-06-02 08:52:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:22:16 +0630","user_id":1} 
[2025-06-02 09:03:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:33:16 +0630","user_id":1} 
[2025-06-02 09:04:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:34:52 +0630","user_id":1} 
[2025-06-02 09:05:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:35:04 +0630","user_id":1} 
[2025-06-02 09:07:02] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:37:01 +0630","user_id":1} 
[2025-06-02 09:07:33] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:37:33 +0630","user_id":1} 
[2025-06-02 09:08:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:38:00 +0630","user_id":1} 
[2025-06-02 09:08:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:38:36 +0630","user_id":1} 
[2025-06-02 09:08:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:38:42 +0630","user_id":1} 
[2025-06-02 09:09:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:39:57 +0630","user_id":1} 
[2025-06-02 09:10:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:40:14 +0630","user_id":1} 
[2025-06-02 09:10:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:40:43 +0630","user_id":1} 
[2025-06-02 09:11:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:41:55 +0630","user_id":1} 
[2025-06-02 09:12:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:42:03 +0630","user_id":1} 
[2025-06-02 09:12:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:42:53 +0630","user_id":1} 
[2025-06-02 09:13:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:43:07 +0630","user_id":1} 
[2025-06-02 09:13:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:43:26 +0630","user_id":1} 
[2025-06-02 09:13:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:43:38 +0630","user_id":1} 
[2025-06-02 09:14:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:44:22 +0630","user_id":1} 
[2025-06-02 09:15:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:45:08 +0630","user_id":1} 
[2025-06-02 09:15:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:45:38 +0630","user_id":1} 
[2025-06-02 09:16:54] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:46:53 +0630","user_id":1} 
[2025-06-02 09:17:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:47:55 +0630","user_id":1} 
[2025-06-02 09:18:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:48:18 +0630","user_id":1} 
[2025-06-02 09:20:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:50:08 +0630","user_id":1} 
[2025-06-02 09:20:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:50:18 +0630","user_id":1} 
[2025-06-02 09:21:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:51:28 +0630","user_id":4} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:21:37] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-02 09:21:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:51:38 +0630","user_id":4} 
[2025-06-02 09:23:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:53:39 +0630","user_id":1} 
[2025-06-02 09:25:17] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:55:17 +0630","user_id":1} 
[2025-06-02 09:25:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:55:36 +0630","user_id":1} 
[2025-06-02 09:26:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 14:56:52 +0630","user_id":1} 
[2025-06-02 09:30:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:00:48 +0630","user_id":1} 
[2025-06-02 09:32:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:02:43 +0630","user_id":1} 
[2025-06-02 09:35:25] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:05:25 +0630","user_id":1} 
[2025-06-02 09:35:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:05:52 +0630","user_id":1} 
[2025-06-02 09:37:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:07:31 +0630","user_id":1} 
[2025-06-02 09:37:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:07:51 +0630","user_id":4} 
[2025-06-02 09:38:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:08:02 +0630","user_id":1} 
[2025-06-02 09:38:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:08:05 +0630","user_id":1} 
[2025-06-02 09:38:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:08:08 +0630","user_id":1} 
[2025-06-02 09:39:06] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:09:05 +0630","user_id":3} 
[2025-06-02 09:39:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:09:11 +0630","user_id":3} 
[2025-06-02 09:39:17] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:09:16 +0630","user_id":3} 
[2025-06-02 09:39:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:09:21 +0630","user_id":1} 
[2025-06-02 09:39:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:09:52 +0630","user_id":1} 
[2025-06-02 09:40:14] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:10:14 +0630","user_id":3} 
[2025-06-02 09:40:20] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-02 09:40:20] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":1} 
[2025-06-02 09:40:20] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-02 09:40:20] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":2} 
[2025-06-02 09:40:20] local.INFO: Blank row detected {"row_number":4,"consecutive_blank_rows":3} 
[2025-06-02 09:40:20] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":3} 
[2025-06-02 09:40:20] local.INFO: Blank row detected {"row_number":5,"consecutive_blank_rows":4} 
[2025-06-02 09:40:20] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":4} 
[2025-06-02 09:40:20] local.INFO: Blank row detected {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-02 09:40:20] local.INFO: 5 or more consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-02 09:40:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:10:22 +0630","user_id":3} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 09:40:27] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-02 09:40:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:10:29 +0630","user_id":3} 
[2025-06-02 09:40:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:10:38 +0630","user_id":3} 
[2025-06-02 09:40:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:10:40 +0630","user_id":3} 
[2025-06-02 09:40:45] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 09:40:48] local.INFO: Staff bulk import completed {"import_id":112,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 09:40:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:10:50 +0630","user_id":1} 
[2025-06-02 09:47:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:17:54 +0630","user_id":1} 
[2025-06-02 09:47:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:17:58 +0630","user_id":1} 
[2025-06-02 09:48:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:00 +0630","user_id":3} 
[2025-06-02 09:48:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:04 +0630","user_id":3} 
[2025-06-02 09:48:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:20 +0630","user_id":3} 
[2025-06-02 09:48:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:23 +0630","user_id":1} 
[2025-06-02 09:48:25] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:24 +0630","user_id":1} 
[2025-06-02 09:48:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:28 +0630","user_id":1} 
[2025-06-02 09:48:33] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:33 +0630","user_id":1} 
[2025-06-02 09:48:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:18:37 +0630","user_id":1} 
[2025-06-02 09:49:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:19:38 +0630","user_id":1} 
[2025-06-02 09:49:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:19:42 +0630","user_id":1} 
[2025-06-02 09:49:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:19:45 +0630","user_id":1} 
[2025-06-02 09:49:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:19:53 +0630","user_id":3} 
[2025-06-02 09:49:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:19:58 +0630","user_id":3} 
[2025-06-02 09:50:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:20:11 +0630","user_id":1} 
[2025-06-02 09:50:18] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 09:50:19] local.INFO: Staff bulk import completed {"import_id":113,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 09:50:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:20:21 +0630","user_id":1} 
[2025-06-02 09:50:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:20:27 +0630","user_id":1} 
[2025-06-02 09:51:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:21:11 +0630","user_id":1} 
[2025-06-02 09:51:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:21:33 +0630","user_id":1} 
[2025-06-02 09:51:42] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 09:51:43] local.INFO: Staff bulk import completed {"import_id":114,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 09:51:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:21:45 +0630","user_id":1} 
[2025-06-02 09:54:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:24:22 +0630","user_id":1} 
[2025-06-02 09:54:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:24:30 +0630","user_id":1} 
[2025-06-02 09:54:33] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 09:54:33] local.INFO: Staff bulk import completed {"import_id":115,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 09:54:34] local.ERROR: Undefined variable $providers {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-form.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#569</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-95810592 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"18 characters\">Prescriptions List</span>\"
</pre><script>Sfdump(\"sf-dump-95810592\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import_id":"<pre class=sf-dump id=sf-dump-733665251 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"3 characters\">115</span>\"
</pre><script>Sfdump(\"sf-dump-733665251\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","has_back":"<pre class=sf-dump id=sf-dump-849379204 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"29 characters\">http://localhost:8000/imports</span>\"
</pre><script>Sfdump(\"sf-dump-849379204\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","scriptsCount":"<pre class=sf-dump id=sf-dump-2094686267 data-indent-pad=\"  \"><span class=sf-dump-num>9</span>
</pre><script>Sfdump(\"sf-dump-2094686267\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statusText":"<pre class=sf-dump id=sf-dump-604128629 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"3 characters\">New</span>\"
</pre><script>Sfdump(\"sf-dump-604128629\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentStep":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","totalSteps":"<pre class=sf-dump id=sf-dump-594415746 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-594415746\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $providers at C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-form.blade.php:51)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $providers at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4ffeb97537e52e853b2e4cb57c5da875.php:56)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 56)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4ffeb97537e52e853b2e4cb57c5da875.php(56): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 56)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#64 {main}
"} 
[2025-06-02 09:58:13] local.ERROR: Call to undefined relationship [import,user] on model [App\Models\ImportFile]. {"userId":1,"exception":"[object] (Illuminate\\Database\\Eloquent\\RelationNotFoundException(code: 0): Call to undefined relationship [import,user] on model [App\\Models\\ImportFile]. at C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\RelationNotFoundException.php:35)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(806): Illuminate\\Database\\Eloquent\\RelationNotFoundException::make(Object(App\\Models\\ImportFile), 'import,user')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(110): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(802): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(776): Illuminate\\Database\\Eloquent\\Builder->getRelation('import,user')
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(756): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'import,user', Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(724): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#6 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\ExcelImportController.php(2282): Illuminate\\Database\\Eloquent\\Builder->get()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ExcelImportController->staffBulkImportPreview(Object(Illuminate\\Http\\Request), '115')
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('staffBulkImport...', Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ExcelImportController), 'staffBulkImport...')
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#60 {main}
"} 
[2025-06-02 09:58:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:28:26 +0630","user_id":1} 
[2025-06-02 10:10:22] local.ERROR: Class "User" not found {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\imports\\index.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-79167890 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#573</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-79167890\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-1408873174 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"7 characters\">Imports</span>\"
</pre><script>Sfdump(\"sf-dump-1408873174\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Class \"User\" not found at C:\\KodeCreators\\newlife-panel\\resources\\views\\imports\\index.blade.php:96)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require()
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (Error(code: 0): Class \"User\" not found at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\ba0c4987e467e7942caadfea2dbf3583.php:96)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require()
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}
"} 
[2025-06-02 10:11:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:41:33 +0630","user_id":1} 
[2025-06-02 10:11:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:41:39 +0630","user_id":1} 
[2025-06-02 10:11:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:41:49 +0630","user_id":1} 
[2025-06-02 10:11:52] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 10:11:52] local.INFO: Staff bulk import completed {"import_id":116,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 10:11:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:41:55 +0630","user_id":1} 
[2025-06-02 10:12:40] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:42:40 +0630","user_id":1} 
[2025-06-02 10:13:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:43:27 +0630","user_id":1} 
[2025-06-02 10:21:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:51:15 +0630","user_id":1} 
[2025-06-02 10:21:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:51:36 +0630","user_id":1} 
[2025-06-02 10:24:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:54:56 +0630","user_id":1} 
[2025-06-02 10:26:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:56:33 +0630","user_id":1} 
[2025-06-02 10:26:35] local.ERROR: Method App\Http\Livewire\Settings\FaxOptions::resetErrorBags does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method App\\Http\\Livewire\\Settings\\FaxOptions::resetErrorBags does not exist. at C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php:320)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(71): Livewire\\Component->__call('resetErrorBags', Array)
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->addNewFaxNumber()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('addNewFaxNumber', Array, Object(Closure))
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#56 {main}
"} 
[2025-06-02 10:27:48] local.INFO: Device time stored in session {"device_time":"2025-06-02 15:57:48 +0630","user_id":1} 
