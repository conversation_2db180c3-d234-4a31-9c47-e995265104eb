<div>
    @if (session()->has('message'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('message') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    @endif

    <form wire:submit.prevent="savePrescription">
        <x-layout.row>
            <div class="col">
                {{-- <x-card> --}}
                <x-card.body>
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            {{-- <h5 class="section-title">Patient Information</h5> --}}

                            <!-- Script Date -->
                            <div class="mb-3">
                                <x-form.input.text label="Script Date" labelRequired="1" model="importFile.script_date"
                                    id="script_date" type="date" />
                                {{-- <small class="text-muted">Automatically filled with today's date but editable.</small> --}}
                            </div>


                            <!-- Last Name -->
                            <div class="mb-3">
                                <x-form.input.text label="Last Name" labelRequired="1" model="importFile.last_name" />
                            </div>

                            <!-- First Name -->
                            <div class="mb-3">
                                <x-form.input.text label="First Name" labelRequired="1" model="importFile.first_name" />
                            </div>

                            <!-- Date of Birth -->
                            <div class="mb-3">
                                <x-form.input.text label="Date of Birth" labelRequired="1" model="importFile.dob"
                                    type="date" />
                            </div>

                            <!-- Gender -->
                            <div class="mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select wire:model="importFile.gender" id="gender" class="form-control gender-select">
                                    <option value="">Select Gender</option>
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                    {{-- <option value="Other">Other</option> --}}
                                </select>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            {{-- <h5 class="section-title">Contact Information</h5> --}}

                            <div class="mb-3">
                                <x-form.input.text label="Address" labelRequired="1" model="importFile.address" />
                            </div>

                            <div class="mb-3">
                                <x-form.input.text label="City" labelRequired="1" model="importFile.city" />
                            </div>

                            <div class="mb-7">
                                <label for="state" class="form-label">State</label>
                                <select wire:model="importFile.state" id="state" class="form-control gender-select">
                                    <option value="">Select State</option>
                                    @foreach ($states as $state)
                                    <option value="{{ $state->id }}">{{ $state->name }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-3">
                                <x-form.input.text label="Zip Code" labelRequired="1" model="importFile.zip" />
                            </div>

                            <div class="mb-3">
                                <x-form.input.text label="Phone" labelRequired="0" model="importFile.phone"
                                    type="number" />
                            </div>
                        </div>
                    </div>

                    <!-- Medication Info -->
                    <div class="row mt-4">
                        <div class="col-12">
                            {{-- <h5 class="section-title">Medication Information</h5> --}}
                            <div class="mb-3">
                                <div class="form-group">
                                    <label labelRequired="1" for="medication" class="form-label">
                                        Medication
                                        <span class="text-danger">*</span>
                                    </label>
                                    <select label="medication"
                                        class="form-control @error('importFile.medication') is-invalid @enderror gender-select"
                                        wire:model="importFile.medication" placeholder="Select medication">
                                        <option value="" selected>Select medication</option>
                                        @foreach ($medications as $medication)
                                        <option value="{{ $medication->name }}">{{ $medication->name }}</option>
                                        @endforeach
                                    </select>

                                    @error('importFile.medication')
                                    <span class="form-text text-danger"><strong>{{ $message }}</strong></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="mb-3">
                                <x-form.input.text label="Strength" labelRequired="1" model="importFile.stregnth" />
                            </div>

                            <div class="mb-3">
                                <x-form.input.text label="Dosing" labelRequired="1" model="importFile.dosing" />
                            </div>

                            <div class="mb-3">
                                <x-form.input.text label="Refills" labelRequired="0" model="importFile.refills" />
                            </div>

                            <div class="mb-3">
                                <x-form.input.text label="Vial Quantity" labelRequired="0"
                                    model="importFile.vial_quantity" />
                            </div>
                        </div>
                    </div>

                    <input type="hidden" id="comment" value="{{ $importFile->comment }}">
                    <input type="hidden" id="operator_name" value="{{ $importFile->returnedByUser ? $importFile->returnedByUser->first_name . ' ' . $importFile->returnedByUser->last_name : '' }}">
                    <input type="hidden" id="operator_email" value="{{ $importFile->returnedByUser ? $importFile->returnedByUser->email : '' }}">

                    <!-- Additional Information -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            {{-- <h5 class="section-title">Additional Information</h5> --}}
                            <div class="mb-3">
                                <x-form.input.textarea label="SIG" labelRequired="0" model="importFile.sig" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            {{-- <h5 class="section-title">Notes</h5> --}}
                            <div class="mb-3">
                                <x-form.input.textarea label="Note" labelRequired="0" model="importFile.notes"
                                    rows="3" />
                            </div>
                        </div>
                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    @if($importFile->id)
                    <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                        wire:target="savePrescription">
                        <span wire:loading.remove wire:target="saveEditedPrescription">Save</span>
                        <span wire:loading wire:target="saveEditedPrescription" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    <button type="button" class="btn btn-primary px-5" wire:click="saveAndSignPrescription"
                        wire:loading.attr="disabled" wire:target="saveAndSignPrescription">
                        <span wire:loading.remove wire:target="saveAndSignPrescription">Save & Sign</span>
                        <span wire:loading wire:target="saveAndSignPrescription" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    @else
                    <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                        wire:target="savePrescription">
                        <span wire:loading.remove wire:target="savePrescription">Save</span>
                        <span wire:loading wire:target="savePrescription" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    @endif

                    <a href="{{ route('scripts.ready-to-sign') }}" class="btn btn-outline-secondary px-5">Cancel</a>

                </x-card.footer>
                {{-- </x-card> --}}
            </div>
        </x-layout.row>
    </form>

</div>

@push('scripts')
<!-- Select2 CSS & JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    const comment = $('#comment').val();
    const operatorName = $('#operator_name').val();
    const operatorEmail = $('#operator_email').val();

    if (comment) {
        // Display comment in red alert container
        let commentHtml = `<div class="alert alert-danger mb-3 p-3">
  <div class="w-100 text-break" style="word-break: break-word;">
    <strong>Comment:</strong> ${comment}
  </div>
</div>`;

        // Add operator information below without container if available
        if (operatorName && operatorEmail) {
            commentHtml += `<div class="text-right mb-3">
  <span><strong>${operatorName}</strong> - ${operatorEmail}</span>
</div>`;
        }

        $('.card-header').html(commentHtml);
    }
    document.addEventListener("livewire:load", function() {
        initGenderSelect();

        Livewire.hook('message.processed', () => {
            initGenderSelect();
        });

        function initGenderSelect() {
            let $select = $('.gender-select');

            // Destroy previous instance to avoid duplicates
            $select.select2('destroy');

            // Re-initialize
            $select.select2({
                dropdownParent: $select.parent(), // keeps dropdown below
                dropdownPosition: 'below',
                placeholder: "Select Gender",
                minimumResultsForSearch: Infinity // hides search box
            });
        }

        // Also set default date after Livewire updates (for new records)
        Livewire.hook('message.processed', () => {
            setDefaultScriptDate();
        });
    });
</script>

<style>
    .select2-container--default .select2-selection--single {
        display: flex;
        align-items: center;
        height: 38px;
        /* match your input height */
    }

    .select2-selection__rendered {
        line-height: 1.6 !important;
    }
</style>
@endpush