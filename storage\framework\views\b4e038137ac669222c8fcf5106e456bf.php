<div>
    <form wire:submit.prevent="store">

        <div class="card-body">
            <div class="row">
                <div class="form-group col-12">
                    <label class="font-size-lg font-weight-bold">Destination Fax Numbers</label>
                    <small class="form-text text-muted mb-3">Add multiple fax numbers to send Scripts to multiple
                        destinations at once.</small>

                    <!-- Existing Fax Numbers -->
                    <div class="mb-5">
                        <?php if($faxNumbers && count($faxNumbers) > 0): ?>
                            <?php $__currentLoopData = $faxNumbers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $faxNumber): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="d-flex mb-3 p-3 border rounded">
                                    <div class="mr-3 d-flex flex-row w-100">
                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="faxNumbers.<?php echo e($index); ?>.label"
                                                class="form-control <?php $__errorArgs = ['faxNumbers.' . $index . '.label'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="Enter Label">
                                            <?php $__errorArgs = ['faxNumbers.' . $index . '.label'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="faxNumbers.<?php echo e($index); ?>.numbers"
                                                class="form-control <?php $__errorArgs = ['faxNumbers.' . $index . '.numbers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="Enter fax number (e.g., 1234567890)" maxlength="15">
                                            <?php $__errorArgs = ['faxNumbers.' . $index . '.numbers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                    </div>
                                    <button type="button" class="mh-50 btn btn-sm btn-light-danger"
                                        onclick="deleteFaxNumber(<?php echo e($index); ?>)"
                                        title="Delete this fax number"
                                        <?php if(empty($faxNumber['numbers'])): ?> disabled <?php endif; ?>>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="alert"
                                style="background-color: #dff9ff; border-color: #b8e6f2; color: #0c5460;">
                                <i class="fas fa-info-circle mr-2" style="color: black;"></i>
                                No fax numbers configured. Add your first destination fax number below.
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- New Fax Number Input Fields -->
                    <?php if($newFaxInputs && count($newFaxInputs) > 0): ?>
                        <div class="border-top pt-4 mb-4">
                            <h6 class="font-weight-bold mb-3">New Fax Numbers</h6>
                            <?php $__currentLoopData = $newFaxInputs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $newFaxInput): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="d-flex align-items-center mb-3 p-3 border rounded bg-light">
                                    <div class="d-flex flex-row mr-3 w-100">
                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="newFaxInputs.<?php echo e($index); ?>.label"
                                                class="form-control <?php $__errorArgs = ['newFaxInputs.' . $index . '.label'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="Enter Label">
                                            <?php $__errorArgs = ['newFaxInputs.' . $index . '.label'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="newFaxInputs.<?php echo e($index); ?>.number"
                                                class="form-control <?php $__errorArgs = ['newFaxInputs.' . $index . '.number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="Enter fax number (e.g., 1234567890)" maxlength="15"
                                                inputmode="numeric">
                                            <?php $__errorArgs = ['newFaxInputs.' . $index . '.number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <button type="button" class="btn btn-sm btn-light-danger"
                                            onclick="removeNewFaxInput(<?php echo e($index); ?>)"
                                            title="Remove this fax number field">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <small class="form-text text-muted mb-3">
                                <i class="fas fa-info-circle mr-1"></i>
                                These fax numbers will be saved when you click "Save Fax Numbers" button.
                            </small>
                        </div>
                    <?php endif; ?>

                    <!-- Add New Fax Number Section -->
                    <div class="border-top pt-4">
                        <h6 class="font-weight-bold mb-3">Add New Fax Number</h6>
                        <div class="d-flex align-items-center">
                            <button type="button" class="btn btn-primary" wire:click="addNewFaxNumber">
                                <i class="fas fa-plus mr-1"></i> Add Number Field
                            </button>
                        </div>
                        <small class="form-text text-muted">
                            Click to add a new fax number input field, then fill it in and save.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-footer d-flex justify-content-between align-items-center">
            <?php if (isset($component)) { $__componentOriginald2555272519db269fb0587805fbfa3da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald2555272519db269fb0587805fbfa3da = $attributes; } ?>
<?php $component = App\View\Components\BtnWithLoading::resolve(['target' => 'store','text' => 'Save Fax Numbers','type' => 'submit'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('btn-with-loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\BtnWithLoading::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $attributes = $__attributesOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__attributesOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $component = $__componentOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__componentOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
        </div>

    </form>


    <script>
        function deleteFaxNumber(index) {
            // Check if the fax number field is empty
            const faxNumberInput = document.querySelector(`input[wire\\:model="faxNumbers.${index}.numbers"]`);
            if (faxNumberInput && !faxNumberInput.value.trim()) {
                Swal.fire({
                    title: 'Cannot Delete',
                    text: "Cannot delete empty fax number. Please enter a valid number first.",
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            Swal.fire({
                title: 'Are you sure?',
                text: "You want to delete this fax number?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.livewire.find('<?php echo e($_instance->id); ?>').call('deleteFaxNumber', index);
                }
            });
        }

        function removeNewFaxInput(index) {
            Swal.fire({
                title: 'Remove Field?',
                text: "Are you sure you want to remove this fax number field?",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Remove it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.livewire.find('<?php echo e($_instance->id); ?>').call('removeNewFaxInput', index);
                }
            });
        }
    </script>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/settings/fax-options.blade.php ENDPATH**/ ?>