<?php

use App\Http\Controllers\ExcelImportController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ExcelToPdfController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

//this route is just for a quickly test anything, please ignore it
Route::get('/test', 'App\Http\Controllers\TestController@test');
Route::get('/test-merge', 'App\Http\Controllers\TestController@mergePdf');



Route::get('/home', function () {
    return redirect('/dashboard');
});
Route::get('/', function () {
    return redirect('/dashboard');
});

Auth::routes();

Route::get('/forgot-password', function () {
    return view('auth.forgot');
})->name('forgot-password');

Route::post('/forgot-password', 'App\Http\Controllers\Admin\HomeController@forgotSendEmail')->name('forgot-password-post');

Route::group(['middleware' => ['auth', 'force.password.change'], 'namespace' => 'App\Http\Controllers'], function () {


    Route::post('/store-device-time', [ExcelImportController::class, 'storeDeviceTime'])->name('store-device-time');

    Route::get('/new-import', [ExcelImportController::class, 'newImport'])->name('excel.new-import');
    Route::get('/view-pdf/{id}', [ExcelImportController::class, 'viewPdf'])->name('excel.view-pdf');


    // Excel to PDF conversion routes
    Route::get('/excel-import', [ExcelImportController::class, 'import'])->name('excel.import');
    Route::post('/excel-import', [ExcelImportController::class, 'store'])->name('excel.store');
    Route::post('/excel-import/process', [ExcelImportController::class, 'process'])->name('excel.process');
    Route::get('/excel-import/download-pdf/{id?}', [ExcelImportController::class, 'downloadPdf'])->name('excel.download-pdf');
    Route::post('/excel-import/download-selected-pdf', [ExcelImportController::class, 'downloadSelectedPdf'])->name('excel.download-selected-pdf');
    Route::match(['get', 'post'], '/excel-import/download-all-pdf/{importId?}', [ExcelImportController::class, 'downloadAllPdf'])->name('excel.download-all-pdf');
    Route::post('/prescription/update-status', [ExcelImportController::class, 'updateStatus'])->name('prescription.update-status');
    Route::post('/prescription/get-import-id', [ExcelImportController::class, 'getImportId'])->name('prescription.get-import-id');
    Route::get('/excel-import/signed/{importId?}', [ExcelImportController::class, 'viewSigned'])->name('excel.view-signed');
    Route::get('/excel-import/sent/{importId?}', [ExcelImportController::class, 'viewSent'])->name('excel.view-sent');
    Route::get('/excel-to-pdf', [ExcelToPdfController::class, 'index'])->name('excel-to-pdf.index');
    Route::post('/excel-to-pdf/convert', [ExcelToPdfController::class, 'convert'])->name('excel-to-pdf.convert');
    Route::get('/excel-import/{id?}', [ExcelImportController::class, 'view'])->name('excel.view');
    Route::post('/excel-import/api/{importId?}', [ExcelImportController::class, 'prescriptionListApi'])->name('prescription-list.api');

    // Staff bulk import routes
    Route::get('/staff-bulk-import', [ExcelImportController::class, 'staffBulkImport'])->name('excel.staff-bulk-import');
    Route::post('/staff-bulk-import', [ExcelImportController::class, 'staffBulkImportStore'])->name('excel.staff-bulk-import.store');
    Route::post('/staff-bulk-import/process', [ExcelImportController::class, 'staffBulkImportProcess'])->name('excel.staff-bulk-import.process');
    Route::get('/staff-bulk-import/view/{id}', [ExcelImportController::class, 'staffBulkImportView'])->name('excel.staff-bulk-import.view');
    Route::get('/api/providers', [ExcelImportController::class, 'getProviders'])->name('api.providers');

    Route::get('/dashboard', 'Admin\HomeController@index')->name('dashboard');


    Route::group(['prefix' => 'imports', 'as' => 'imports.'], function () {
        Route::get('/', 'Admin\ImportController@index')->name('index');
        Route::post('/api', 'Admin\ImportController@indexWeb')->name('api');
        Route::get('/api-pagination', 'Admin\ImportController@pagination')->name('api.pagination');
    });

    Route::group(['prefix' => 'users', 'as' => 'users.'], function () {
        Route::get('/', 'Admin\UserController@index')->name('index');
        Route::get('/create', 'Admin\UserController@create')->name('create');
        Route::get('/{user}/edit', 'Admin\UserController@edit')->name('edit');
        Route::post('/{user}/delete', 'Admin\UserController@delete')->name('delete');
        Route::post('/api', 'Admin\UserController@indexWeb')->name('api');
        Route::get('/api-pagination', 'Admin\UserController@pagination')->name('api.pagination');
        Route::get('/{user}/statusChange', 'Admin\UserController@change_status')->name('status');
        Route::post('/send-temp-password', 'Admin\UserController@sendTempPassword')->name('send-temp-password');
    });

    Route::group(['prefix' => 'admin', 'as' => 'admin.', 'middleware' => ['auth', 'admin']], function () {
        Route::get('/', 'Admin\AdminController@index')->name('index');
        Route::get('/create', 'Admin\AdminController@create')->name('create');
        Route::get('/{user}/edit', 'Admin\AdminController@edit')->name('edit');
        Route::post('/{user}/delete', 'Admin\AdminController@delete')->name('delete');
        Route::post('/api', 'Admin\AdminController@indexWeb')->name('api');
        Route::get('/api-pagination', 'Admin\AdminController@pagination')->name('api.pagination');
        Route::get('/{user}/statusChange', 'Admin\AdminController@change_status')->name('status');
    });

    // SETTINGS
    Route::group(['prefix' => 'settings', 'as' => 'settings.'], function () {
        Route::get('change-password', 'Admin\SettingController@change_password')->name('change-password');
        Route::get('fax-options', 'Admin\SettingController@faxOptions')->name('fax-options');

        Route::group(['prefix' => 'terms-and-conditions', 'as' => 'terms.'], function () {
            Route::get('/', 'Admin\SettingController@terms_and_conditions')->name('index');
        });

        Route::group(['prefix' => 'privacy-policy', 'as' => 'privacy.'], function () {
            Route::get('/', 'Admin\SettingController@privacy_policy')->name('index');
        });

        Route::group(['prefix' => 'about-us', 'as' => 'about.'], function () {
            Route::get('/', 'Admin\SettingController@aboutUs')->name('index');
        });
    });

    // SCRIPTS
    Route::group(['prefix' => 'scripts', 'as' => 'scripts.'], function () {
        Route::get('/all', 'Admin\ScriptController@all')->name('all');
        Route::get('/ready-to-sign', 'Admin\ScriptController@readyToSign')->name('ready-to-sign');
        Route::get('/{importFile}/edit', 'Admin\ScriptController@edit')->name('edit');
        Route::get('/ready-to-send/approval', 'Admin\ScriptController@readyToSend')->name('ready-to-send');
        Route::get('/ready-to-send', 'Admin\ScriptController@pendingApproval')->name('pending-approval');
        Route::get('/sent', 'Admin\ScriptController@sent')->name('sent');
        Route::get('/provider-pending-approval', 'Admin\ScriptController@providerPendingApproval')->name('provider-pending-approval');

        Route::get('view/{importId?}', 'Admin\ScriptController@preview')->name('preview');

        Route::post('/api/all', 'Admin\ScriptController@indexWebAll')->name('api.all');
        Route::post('/api/ready-to-sign', 'Admin\ScriptController@indexWebReadyToSign')->name('api.ready-to-sign');
        Route::post('/api/ready-to-send', 'Admin\ScriptController@indexWebReadyToSend')->name('api.ready-to-send');
        Route::post('/api/pending-approval', 'Admin\ScriptController@indexPendingApproval')->name('api.pending-approval');
        Route::post('/api/sent', 'Admin\ScriptController@indexSent')->name('api.sent');
        Route::post('/api/provider-pending-approval', 'Admin\ScriptController@indexProviderPendingApproval')->name('api.provider-pending-approval');
        Route::post('/api/return-script', 'Admin\ScriptController@returnScript')->name('api.return-script');


        Route::match(['get', 'post'], '/api/download-all-pdf/{importId?}', 'Admin\ScriptController@downloadAllPdf')->name('download-all-pdf');
        Route::post('/sign-all', 'Admin\ScriptController@signAll')->name('sign-pdf');
        Route::post('/send-all', 'Admin\ScriptController@sendAllForApproval')->name('send-for-approval');
        Route::post('send-fax', 'Admin\ScriptController@sendFaxAll')->name('send-fax');
        Route::get('/{importFile}/show-pdf', 'Admin\ScriptController@showPdf')->name('show-pdf');
        Route::post('/delete/{importFile}', 'Admin\ScriptController@delete')->name('delete');
    });

    Route::group(['prefix' => 'archive', 'as' => 'archive.'], function () {
        Route::get('/', 'ArchiveController@index')->name('index');
        Route::get('/show-pdf/{id?}', 'ArchiveController@show')->name('show-pdf');
        Route::post('/api', 'ArchiveController@indexWeb')->name('api');
        Route::match(['get', 'post'], '/download-all-pdf/{importId?}', 'ArchiveController@downloadAllPdf')->name('download-all-pdf');
        Route::get('/download/{id?}', 'ArchiveController@download')->name('file-download');
        Route::get('/show-all-pdf/{importId?}', 'ArchiveController@preview')->name('show-all-pdf');
        Route::get('/sign/{id?}', 'ArchiveController@sign')->name('sign-pdf');
        Route::get('send-fax/{id?}', 'ArchiveController@sendFax')->name('send-fax');
    });
});
