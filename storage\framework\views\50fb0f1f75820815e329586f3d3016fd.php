

<?php $__env->startSection('content'); ?>
    <?php
        use App\Models\User;
    ?>

    <?php if(Auth::user()->role === User::ROLE_ADMIN): ?>
        <div class="row">
            <div class="col-12 col-md-4 col-lg-2">
                <a href="<?php echo e(route('users.create')); ?>" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-plus icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Add Provider</h4>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-12 col-md-4 col-lg-2">
                <a href="<?php echo e(route('excel.staff-bulk-import')); ?>" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-success h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-file-import icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Bulk Import</h4>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    <?php elseif(Auth::user()->role === User::ROLE_OPERATOR): ?>
        <div class="row">
            <div class="col-12 col-md-4 col-lg-2">
                <a href="<?php echo e(route('users.create')); ?>" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-plus icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Add Provider</h4>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-12 col-md-4 col-lg-2">
                <a href="<?php echo e(route('excel.staff-bulk-import')); ?>" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-success h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-file-import icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Bulk Import</h4>
                        </div>
                    </div>
                </a>
            </div>

            <?php if($readyToSendCount > 0): ?>
                <div class="col-12 col-md-4 col-lg-2">
                    <a href="<?php echo e(route('scripts.ready-to-send')); ?>" class="text-decoration-none">
                        <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                            <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                                <i class="fa fa-paper-plane icon-3x mb-3"></i>
                                <h4 class="font-weight-bold">Send Scripts</h4>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    <?php elseif(Auth::user()->role === User::ROLE_PROVIDER): ?>
        <div class="row">
            <div class="col-12 col-md-4 col-lg-2">
                <a href="<?php echo e(route('excel.store')); ?>" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-file-import icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Bulk Import</h4>
                        </div>
                    </div>
                </a>
            </div>


            <?php if($readyToSignCount > 0): ?>
                <div class="col-12 col-md-4 col-lg-2">
                    <a href="<?php echo e(route('scripts.ready-to-sign')); ?>" class="text-decoration-none">
                        <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                            <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                                <i class="fas fa-pen-nib icon-3x mb-3"></i>
                                <h4 class="font-weight-bold">Sign Scripts</h4>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- <?php if (isset($component)) { $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.card','data' => ['title' => 'Imports','value' => ''.e($user_count).'','icon' => 'fa fa-users']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('dashboard.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Imports','value' => ''.e($user_count).'','icon' => 'fa fa-users']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $attributes = $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $component = $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?> -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/dashboard.blade.php ENDPATH**/ ?>