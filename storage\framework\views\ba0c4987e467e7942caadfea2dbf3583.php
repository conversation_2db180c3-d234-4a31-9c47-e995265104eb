

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="imports_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-12 col-sm">
                    <a href="#" id="import-link">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            <span>Add Imports</span>
                        </button>
                    </a>
                </div>





            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="imports_dt"></div>

        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        // Get the base URL from the current window
        const baseUrl = window.location.origin; // This will get something like http://127.0.0.1:8000
        const importPath = '/excel-import'; // Using the multi-step import wizard

        // Construct the full URL
        const fullUrl = baseUrl + importPath;

        // Set the href of the link
        document.getElementById('import-link').href = fullUrl;
    </script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `<?php echo e(url('/storage')); ?>`;
        const apiRoute = `<?php echo e(route('imports.api')); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";

        datatableElement = $('#imports_dt');
        searchElement = $('#imports_search');

        columnArray = [
            {
                field: 'file_name',
                title: `File Name`,
                width: 400,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'user.first_name',
                title: `Created By`,
                width: 200,
                sortable: true,
                autoHide: false,
                template: function(data) {
                    if (data.user) {
                        return data.user.first_name + ' ' + (data.user.last_name || '');
                    }
                    return 'Unknown';
                }
            },
            {
                field: 'Prescription List',
                title: 'Prescription List',
                sortable: false,
                width: 130,
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `<a href="<?php echo e(route('excel.view')); ?>/${data.id}" class="btn btn-sm btn-clean btn-icon" title="Prescription List">
                                <i class="menu-icon fas fa-list"></i>
                            </a>`
                }
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            layout: {
                scroll: false,
                footer: false
            },
            sortable: true,
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            columns: columnArray
        });

        function deleteRequest(btn) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to Delete this Import?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete the Import!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', $(btn).data('id')),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        success: function(res) {
                            toastr.success(res.message);
                            datatable.reload();
                        }
                    });
                }
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/imports/index.blade.php ENDPATH**/ ?>