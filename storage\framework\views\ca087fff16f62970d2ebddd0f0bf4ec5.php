

<?php
use App\Models\ImportFile;

?>
<?php $__env->startSection('content'); ?>
<div class="card card-custom mb-5 p-6">
    <div class="card-body" x-data="{ showFilter: false }">
        <div class="row justify-content-between">
            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                <div class="input-icon">
                    <input type="text" class="form-control" placeholder="Search..." id="ready_to_sign_search" />
                    <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                    </span>
                </div>
            </div>

            <div class="col-auto">
                <button type="button" id="download-all-global-btn" class="btn btn-dark">
                    <i class="fa fa-download mr-1"></i> Download All
                </button>
                <button type="button" id="download-selected-global-btn" class="btn btn-dark">
                    <i class="fa fa-download mr-1"></i> Download Selected
                </button>
                <button type="button" id="sign-all-global-btn" class="btn btn-dark">
                    <i class="fas fa-pen-nib mr-1"></i> Sign All
                </button>
                <button type="button" id="sign-selected-global-btn" class="btn btn-dark">
                    <i class="fas fa-pen-nib mr-1"></i> Sign Selected
                </button>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="script_date_filter">Script Date:</label>
                <div class="input-group date">
                    <input type="date" class="form-control" id="script_date_filter" />
                    <div class="input-group-append">
                        <button class="btn btn-secondary" type="button" id="clear_date_filter">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
            
        <div class="col-md-4 mb-3">
            <label for="medication_filter">Medication:</label>
            <select class="form-control" id="medication_filter">
                <option value="">All Medications</option>
                <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($medication->id); ?>"><?php echo e($medication->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
</div>

<div class="datatable datatable-bordered datatable-head-custom" id="ready_to_sign_dt"></div>

</div>
</div>

<!-- Script Preview Modal -->
<div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-fullscreen" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="script-preview-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="download-preview-btn" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download
                </a>
                <button type="button" id="sign-preview-btn" class="btn btn-success">
                    <i class="fas fa-pen-nib"></i> Sign
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
<style>
    button:disabled {
        cursor: not-allowed !important;
        opacity: 0.6 !important;
        pointer-events: none !important;
    }

    /* Custom styles for the preview modal */
    #scriptPreviewModal .modal-dialog {
        max-width: 95%;
        height: 95vh;
        margin: 1rem auto;
    }

    #scriptPreviewModal .modal-content {
        height: 100%;
        border-radius: 4px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
    }

    #scriptPreviewModal .modal-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
    }

    #scriptPreviewModal .modal-header {
        border-bottom: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
    }

    #scriptPreviewModal .modal-footer {
        border-top: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
        position: relative;
        flex-shrink: 0;
        justify-content: flex-end;
        background-color: #fff;
        z-index: 5;
    }

    #scriptPreviewModal .close {
        cursor: pointer;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        margin: 0;
        padding: 0;
    }

    #scriptPreviewModal .close i {
        font-size: 1rem;
    }

    #scriptPreviewModal .close:hover {
        color: #3699FF;
        background-color: #f3f6f9;
        border-radius: 4px;
    }

    #script-preview-content {
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: relative;
    }

    #script-preview-content iframe {
        width: 100%;
        height: 100%;
        border: none;
        display: block;
    }

    /* Simple style for clickable rows */
    #ready_to_sign_dt tbody tr td:not(:last-child) {
        cursor: pointer;
    }

    /* Highlight on hover */
    #ready_to_sign_dt tbody tr:hover td:not(:last-child) {
        background-color: rgba(54, 153, 255, 0.1) !important;
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
<script>
    var datatable;
    var datatableElement;
    var searchElement;
    var columnArray;

    const storagePath = `<?php echo e(url('/storage')); ?>`;
    const apiRoute = `<?php echo e(route('scripts.api.ready-to-sign')); ?>`;
    let url = "<?php echo e(Storage::url('/')); ?>";
    const signAllRoute = `<?php echo e(route('scripts.sign-pdf')); ?>`;

    datatableElement = $('#ready_to_sign_dt');
    searchElement = $('#ready_to_sign_search');

    columnArray = [{
            field: 'checkbox',
            title: '<label class="checkbox checkbox-single checkbox-all"><input type="checkbox" id="select-all-checkbox" />&nbsp;<span></span></label>',
            sortable: false,
            width: 'auto',
            autoHide: false,
            textAlign: 'center',
            template: function(data) {
                // Check if this item is in the globalSelectedIds array
                const isChecked = data.is_selected || globalSelectedIds.includes(data.id.toString()) ?
                    'checked' : '';
                return `<label class="checkbox checkbox-single">
                    <input type="checkbox" class="row-checkbox" value="${data.id}" ${isChecked} />&nbsp;<span></span>
                </label>`;
            }
        },
        {
            field: 'import_file_name',
            title: `File Name`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return `<div style="white-space: normal; text-wrap: wrap;">${data.import_file_name ?? ''}</div>`;
            }
        },
        {
            field: 'created_at',
            title: `Created At`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
            }
        },
        {
            field: 'signed_at',
            title: `Signed at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.signed_at ? moment(data.signed_at).format('MM/DD/YYYY') :
                    '<b>Not Signed Yet</b>';
            }
        },
        {
            field: 'sent_at',
            title: `Sent at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.sent_at ? moment(data.sent_at).format('MM/DD/YYYY hh:mm A') :
                    '<b>Not Sent Yet</b>';
            }
        },
        {
            field: 'script_date',
            title: `Script date`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return moment(data.script_date).format('MM/DD/YYYY');
            }
        },
        {
            field: 'last_name',
            title: `Last name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'first_name',
            title: `First name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'medication',
            title: `Medication`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'status',
            title: `Status`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                const comment = (data.comment_with_operator || data.comment || '').replace(/"/g, '&quot;').replace(/'/g, '&#39;');
                return `<div class="${data.status.toLowerCase() == 'pending revision' ? 'comment' : ''}" style="white-space: normal; text-wrap: wrap;">${data.status ?? ''}<input type='hidden' value='${comment}' ></div>
                `;
            }
        },
        {
            field: 'Actions',
            title: 'Actions',
            sortable: false,
            width: 'auto',
            overflow: 'visible',
            autoHide: false,
            template: function(row) {
                const downloadRoute = `<?php echo e(route('archive.file-download', ['id' => '::ID'])); ?>`.replace('::ID',
                    row.id);
                const editRoute = `<?php echo e(route('scripts.edit', ['importFile' => '::ID'])); ?>`.replace('::ID',
                    row.id);
                const viewRoute = `<?php echo e(route('archive.show-pdf', ['id' => '::ID'])); ?>`.replace('::ID', row.id);
                const status = row.status.toLowerCase();

                let signButton = '';
                if (status === 'new' || status === 'pending revision') {
                    signButton = `<a href="#" data-record-id="${row.id}" class="btn btn-sm btn-clean btn-icon sign-btn" data-toggle="tooltip" title="Sign & Send for Approval">
                                        <i class="menu-icon fas fa-pen-nib"></i>
                                      </a>`;
                } else {
                    signButton = `<a class="btn btn-sm btn-clean btn-icon disabled" data-toggle="tooltip" title="Already Signed & Sent for Approval">
                                        <i class="menu-icon fas fa-pen-nib text-muted"></i>
                                      </a>`;
                }

                return `
                        <a href="${downloadRoute}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Download">
                            <i class="menu-icon fas fa-download"></i>
                        </a>
                        <a href="${editRoute}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Edit">
                            <i class="menu-icon fas fa-pen"></i>
                        </a>
                        ${signButton}
                        <a href="#" data-id="${row.id}" data-view-route="${viewRoute}" data-download-route="${downloadRoute}" data-toggle="tooltip" class="btn btn-sm btn-clean btn-icon preview-btn" title="View Script">
                            <i class="menu-icon fas fa-eye"></i>
                        </a>
                    `;
            },
        }
    ];


    // Store selected IDs globally
    let globalSelectedIds = [];

    datatable = datatableElement.KTDatatable({
        data: {
            type: 'remote',
            source: {
                read: {
                    url: apiRoute,
                    //sample custom headers
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    params: function() {
                        // Get the current query parameters
                        const query = datatable.getDataSourceQuery();

                        // Get the script date directly from the input field
                        const scriptDateValue = $('#script_date_filter').val();

                        // Get the current search value directly from the search input
                        const searchValue = $(searchElement).val() || '';

                        // Make sure we're sending a valid date format
                        let formattedScriptDate = '';
                        if (scriptDateValue) {
                            // Ensure it's in YYYY-MM-DD format
                            const dateObj = new Date(scriptDateValue);
                            if (!isNaN(dateObj.getTime())) {
                                formattedScriptDate = dateObj.toISOString().split('T')[0];
                            }
                        }

                        // Add selected IDs and filter parameters to the request
                        return {
                            displayed_ids: globalSelectedIds,
                            script_date: formattedScriptDate || query.script_date || '',
                            provider_id: query.provider_id || '',
                            medication_id: query.medication_id || '',
                            // Get the current search value
                            search: $(searchElement).val() || '',
                            // Add query structure for compatibility with backend
                            query: {
                                script_date: formattedScriptDate || query.script_date || '',
                                search: $(searchElement).val() || ''
                            }
                        };
                    },
                    map: function(raw) {
                        // sample data mapping
                        var dataSet = raw;
                        if (typeof raw.data !== 'undefined') {
                            dataSet = raw.data;

                            // Store the selected IDs from the response if available
                            if (raw.meta && raw.meta.selectedIds) {
                                globalSelectedIds = raw.meta.selectedIds;
                            }

                            // Mark items as selected based on is_selected property
                            dataSet.forEach(function(item) {
                                if (item.is_selected) {
                                    // Ensure this ID is in our global selected IDs
                                    if (!globalSelectedIds.includes(item.id)) {
                                        globalSelectedIds.push(item.id);
                                    }
                                }
                            });
                        }
                        return dataSet;
                    },
                },
            },
            pageSize: 10,
            serverPaging: true,
            serverFiltering: true,
            serverSorting: true,
        },
        pagination: true,
        search: {
            input: searchElement,
            key: 'search'
        },
        layout: {
            customScrollbar: false,
            scroll: true,
        },
        columns: columnArray
    });

    // Initialize the datatable query parameters with empty values
    datatable.setDataSourceQuery({
        query: {
            script_date: '',
            provider_id: '',
            medication_id: '',
            search: ''
        }
    });

    // Initialize the "Selected" buttons as disabled by default
    toggleSelectedButtons(true);

    // Add event listeners to search input
    $(searchElement).on('keyup search input', function(e) {
        // Clear selections when search changes
        globalSelectedIds = [];

        // Disable all buttons while loading
        togglePageButtons(true);
        toggleSelectedButtons(true);
    });


    // Function to disable/enable "Download All" and "Sign All" buttons
    // This function is called when there are no records (either no data at all or filters return empty results)
    function togglePageButtons(disable) {
        console.log('togglePageButtons called with disable:', disable);

        // Disable/enable the "All" buttons when there are no records
        $('#download-all-global-btn').prop('disabled', disable);
        $('#sign-all-global-btn').prop('disabled', disable);

        // If disabling all buttons due to empty data, also disable the "Selected" buttons
        if (disable) {
            toggleSelectedButtons(true);
        }
    }

    // Function to toggle "All" buttons when items are selected
    function toggleAllButtonsWhenSelected(hasSelection) {
        // When items are selected, disable "All" buttons
        $('#download-all-global-btn').prop('disabled', hasSelection);
        $('#sign-all-global-btn').prop('disabled', hasSelection);
    }

    // Handle ajax done event
    datatable.on('datatable-on-ajax-done', function(e, data) {
        // Check if data is empty - this handles both no records and filtered results with no matches
        const isEmpty = !data || !data.length || data.length === 0;
        console.log('Ajax done - data length:', data ? data.length : 'null', 'isEmpty:', isEmpty);

        // Disable "Download All" and "Sign All" buttons when there are no records
        togglePageButtons(isEmpty);

        // If there are no records, also clear any selections and disable selected buttons
        if (isEmpty) {
            globalSelectedIds = [];
            toggleSelectedButtons(true);
        }

        // Update the "Select All" checkbox state and button states after data is loaded
        setTimeout(function() {
            

            // Restore checkboxes for items that were previously selected
            restoreSelectedCheckboxes();

            // Update the "Select All" checkbox state
            updateSelectAllCheckboxState();

            // Check if any checkboxes are selected and update the buttons accordingly
            const hasSelectedItems = globalSelectedIds.length > 0;
            toggleSelectedButtons(!hasSelectedItems);

            // Only allow "All" buttons when there are records and no selections
            if (!isEmpty) {
                toggleAllButtonsWhenSelected(hasSelectedItems);
            } else {
                // If there are no records, ensure "All" buttons stay disabled
                console.log('No records found - keeping All buttons disabled');
                $('#download-all-global-btn').prop('disabled', true);
                $('#sign-all-global-btn').prop('disabled', true);
            }

            loadComment();
        }, 100);
    });

    $(window).on('resize', function() {
        setTimeout(() => {
            loadComment();
        }, 200);
    });

    function loadComment() {
        $('.comment').each(function() {
            const commetElement = $(this);
            // const commetElement = $(this).closest('tr');
            const data = $(this).find('input').val();
            const maxLength = 350;
            const formmatedData = data.length > maxLength ? data.slice(0, maxLength) + '....' : data;

            if (data) {
                // commetElement.find('td:not(:last-child)').attr({
                commetElement.attr({
                    'data-toggle': 'tooltip',
                    'title': formmatedData
                });
            }

        });

        initializeTooltips();
    }

    // Handle ajax fail event
    datatable.on('datatable-on-ajax-fail', function(e, jqXHR) {
        // Disable buttons on error
        togglePageButtons(true);
    });

    const routeTemplate = "<?php echo e(route('scripts.download-all-pdf')); ?>";

    $('#download-all-global-btn').on('click', function() {
        // Check if button is disabled - if so, don't proceed
        if ($(this).prop('disabled')) {
            return false;
        }

        const form = $('<form>', {
            method: 'POST',
            action: routeTemplate
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_NEW); ?>' // Using constant from ImportFile model
        }));
        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_PENDING_REVISION); ?>' // Using constant from ImportFile model
        }));

        // Get current filter values and add them to the form
        const scriptDateValue = $('#script_date_filter').val();
        if (scriptDateValue) {
            // Format the date properly
            const dateObj = new Date(scriptDateValue);
            if (!isNaN(dateObj.getTime())) {
                const formattedScriptDate = dateObj.toISOString().split('T')[0];
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'script_date',
                    value: formattedScriptDate
                }));
            }
        }

        const medicationId = $('#medication_filter').val();
        if (medicationId) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'medication_id',
                value: medicationId
            }));
        }

        // Get current search value
        const searchValue = $(searchElement).val();
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#download-selected-global-btn').on('click', function() {
        const selectedIds = getSelectedIds();

        if (selectedIds.length === 0) {
            return;
        }

        const form = $('<form>', {
            method: 'POST',
            action: routeTemplate.replace('__ID__', '') // same endpoint
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        // Optional: pass status filter if needed
        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_NEW); ?>' // Using constant from ImportFile model
        }));
        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_PENDING_REVISION); ?>' // Using constant from ImportFile model
        }));

        // Add displayed_ids[] inputs
        selectedIds.forEach(function(id) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'displayed_ids[]',
                value: id
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#sign-all-global-btn').on('click', function() {
        // Check if button is disabled - if so, don't proceed
        if ($(this).prop('disabled')) {
            return false;
        }

        // Get the current date
        const now = new Date();

        // Create a complete date-time string with timezone information
        // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        // Get timezone offset in minutes and convert to hours and minutes
        const tzOffset = now.getTimezoneOffset();
        const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
        const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
        const tzSign = tzOffset <= 0 ? '+' :
            '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

        // Construct the full datetime string with timezone
        const clientTimestamp =
            `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;

        console.log('Using client timestamp:', clientTimestamp);

        const form = $('<form>', {
            method: 'POST',
            action: signAllRoute
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        // Pass status
        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_NEW); ?>' // dynamically set if needed
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_PENDING_REVISION); ?>' // dynamically set if needed
        }));

        // Pass status
        form.append($('<input>', {
            type: 'hidden',
            name: 'changed_status',
            // value: 'Signed' // dynamically set if needed
            value: '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>'
        }));

        // Pass client timestamp
        form.append($('<input>', {
            type: 'hidden',
            name: 'client_timestamp',
            value: clientTimestamp
        }));

        // Get current filter values and add them to the form
        const scriptDateValue = $('#script_date_filter').val();
        if (scriptDateValue) {
            // Format the date properly
            const dateObj = new Date(scriptDateValue);
            if (!isNaN(dateObj.getTime())) {
                const formattedScriptDate = dateObj.toISOString().split('T')[0];
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'script_date',
                    value: formattedScriptDate
                }));
            }
        }

        const medicationId = $('#medication_filter').val();
        if (medicationId) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'medication_id',
                value: medicationId
            }));
        }

        // Get current search value
        const searchValue = $(searchElement).val();
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#sign-selected-global-btn').on('click', function() {
        const selectedIds = getSelectedIds();

        if (selectedIds.length === 0) {
            return;
        }

        // Get the current date
        const now = new Date();

        // Create a complete date-time string with timezone information
        // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        // Get timezone offset in minutes and convert to hours and minutes
        const tzOffset = now.getTimezoneOffset();
        const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
        const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
        const tzSign = tzOffset <= 0 ? '+' :
            '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

        // Construct the full datetime string with timezone
        const clientTimestamp =
            `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;

        console.log('Using client timestamp:', clientTimestamp);

        const form = $('<form>', {
            method: 'POST',
            action: signAllRoute
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        // Optional: pass status filter if needed
        form.append($('<input>', {
            type: 'hidden',
            name: 'status',
            value: '<?php echo e(ImportFile::STATUS_NEW); ?>' // using constant from ImportFile model
        }));

        // Optional: pass status filter if needed
        form.append($('<input>', {
            type: 'hidden',
            name: 'changed_status',
            // value: 'Signed' // change dynamically if needed
            value: '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>'

        }));

        // Pass client timestamp
        form.append($('<input>', {
            type: 'hidden',
            name: 'client_timestamp',
            value: clientTimestamp
        }));

        // Add displayed_ids[] inputs
        selectedIds.forEach(function(id) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'displayed_ids[]',
                value: id
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });


    // Handle "Select All" checkbox
    $(document).on('change', '#select-all-checkbox', function() {
        let isChecked = $(this).is(':checked');

        // Check/uncheck all visible checkboxes
        $('.row-checkbox').prop('checked', isChecked);

        // Use our improved getSelectedIds function to update the global selection state
        getSelectedIds();

        // Update button states based on selection
        const hasData = $('.row-checkbox').length > 0;

        if (isChecked && hasData) {
            toggleAllButtonsWhenSelected(true);
            toggleSelectedButtons(false);
        } else if (!isChecked) {
            // If there are still selected items on other pages
            if (globalSelectedIds.length > 0) {
                toggleAllButtonsWhenSelected(true);
                toggleSelectedButtons(false);
            } else {
                // If nothing is selected anywhere, only enable "All" buttons if there's data
                if (hasData) {
                    toggleAllButtonsWhenSelected(false);
                } else {
                    // No data available, keep "All" buttons disabled
                    $('#download-all-global-btn').prop('disabled', true);
                    $('#sign-all-global-btn').prop('disabled', true);
                }
                toggleSelectedButtons(true);
            }
        }
    });

    // Handle individual checkbox changes
    $(document).on('change', '.row-checkbox', function() {
        // Use our improved getSelectedIds function to update the global selection state
        getSelectedIds();

        // Update "Select All" checkbox state
        updateSelectAllCheckboxState();
    });

    function getSelectedIds() {
        // Get all visible checkbox IDs on the current page
        let visibleIds = [];
        $('.row-checkbox').each(function() {
            visibleIds.push($(this).val());
        });

        // First, remove any IDs from globalSelectedIds that are visible on the current page
        // This ensures we don't have stale selections
        globalSelectedIds = globalSelectedIds.filter(function(id) {
            return !visibleIds.includes(id);
        });

        // Now add all currently checked IDs to globalSelectedIds
        $('.row-checkbox:checked').each(function() {
            const id = $(this).val();
            globalSelectedIds.push(id);
        });

        // Update the "Select All" checkbox state
        updateSelectAllCheckboxState();

        // Update the "Selected" buttons state - enable when items are selected
        toggleSelectedButtons(globalSelectedIds.length === 0);

        // Check if there are any visible rows (data) before enabling "All" buttons
        const hasData = $('.row-checkbox').length > 0;

        // Update the "All" buttons state - only enable if there's data and no selections
        if (hasData) {
            toggleAllButtonsWhenSelected(globalSelectedIds.length > 0);
        } else {
            // No data available, keep "All" buttons disabled
            $('#download-all-global-btn').prop('disabled', true);
            $('#sign-all-global-btn').prop('disabled', true);
        }

        return globalSelectedIds;
    }

    // Function to toggle the "Selected" buttons based on whether any items are selected
    function toggleSelectedButtons(disable) {
        $('#download-selected-global-btn').prop('disabled', disable);
        $('#sign-selected-global-btn').prop('disabled', disable);
    }

    // Function to update the "Select All" checkbox state
    function updateSelectAllCheckboxState() {
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;

        if (totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes) {
            $('#select-all-checkbox').prop('checked', true);
        } else {
            $('#select-all-checkbox').prop('checked', false);
        }
    }

    // Function to restore selected checkboxes based on globalSelectedIds
    function restoreSelectedCheckboxes() {
        // For each checkbox on the current page
        $('.row-checkbox').each(function() {
            const id = $(this).val();
            // If this ID is in our globalSelectedIds array, check it
            if (globalSelectedIds.includes(id)) {
                $(this).prop('checked', true);
            }
        });
    }

    // Handle pagination events to maintain selection state
    datatable.on('datatable-on-layout-updated', function() {
        // After layout update (which happens on page change), restore selections
        setTimeout(function() {
            restoreSelectedCheckboxes();
            updateSelectAllCheckboxState();

            // Check if there are any visible rows (data)
            const hasData = $('.row-checkbox').length > 0;

            // Update button states based on selection and data availability
            toggleSelectedButtons(globalSelectedIds.length === 0);

            // Only enable "All" buttons if there's data and no selections
            if (hasData) {
                toggleAllButtonsWhenSelected(globalSelectedIds.length > 0);
            } else {
                // No data available, keep "All" buttons disabled
                $('#download-all-global-btn').prop('disabled', true);
                $('#sign-all-global-btn').prop('disabled', true);
            }
        }, 100);
    });

    // Handle datatable reloads
    datatable.on('datatable-on-reloaded', function() {
        // After reload, restore selections
        setTimeout(function() {
            restoreSelectedCheckboxes();
            updateSelectAllCheckboxState();

            // Check if there are any visible rows (data)
            const hasData = $('.row-checkbox').length > 0;

            // Update button states based on selection and data availability
            toggleSelectedButtons(globalSelectedIds.length === 0);

            // Only enable "All" buttons if there's data and no selections
            if (hasData) {
                toggleAllButtonsWhenSelected(globalSelectedIds.length > 0);
            } else {
                // No data available, keep "All" buttons disabled
                $('#download-all-global-btn').prop('disabled', true);
                $('#sign-all-global-btn').prop('disabled', true);
            }
        }, 100);
    });

    // Handle page change events
    datatable.on('datatable-on-goto-page', function(e, meta) {
        // When changing pages, we need to preserve our globalSelectedIds
        // The datatable will reload data, and our ajax-done handler will restore selections
        console.log('Page changed to:', meta.page);
    });

    // Function to capture client device time and submit the sign form
    function captureTimeAndSign(recordId) {
        // Get the current date
        const now = new Date();

        // Create a complete date-time string with timezone information
        // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        // Get timezone offset in minutes and convert to hours and minutes
        const tzOffset = now.getTimezoneOffset();
        const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
        const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
        const tzSign = tzOffset <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

        // Construct the full datetime string with timezone
        const clientTimestamp =
            `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;

        console.log('Using client timestamp:', clientTimestamp);

        // Create a form to submit
        const form = $('<form>', {
            method: 'POST',
            action: signAllRoute
        });

        // Add CSRF token
        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        // Add status
        form.append($('<input>', {
            type: 'hidden',
            name: 'status',
            value: '<?php echo e(ImportFile::STATUS_NEW); ?>'
        }));

        // Add changed status
        form.append($('<input>', {
            type: 'hidden',
            name: 'changed_status',
            value: '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>'
        }));

        // Add client timestamp
        form.append($('<input>', {
            type: 'hidden',
            name: 'client_timestamp',
            value: clientTimestamp
        }));

        // Add the record ID as displayed_ids[]
        form.append($('<input>', {
            type: 'hidden',
            name: 'displayed_ids[]',
            value: recordId
        }));

        // Submit the form
        $('body').append(form);
        form.submit();
        form.remove();
    }

    // Add event listener for sign buttons
    $(document).on('click', '.sign-btn', function(e) {
        e.preventDefault();
        const recordId = $(this).data('record-id');
        captureTimeAndSign(recordId);
    });

    // Add event listener for preview buttons
    $(document).on('click', '.preview-btn', function(e) {
        e.preventDefault();

        const fileId = $(this).data('id');
        const viewRoute = $(this).data('view-route');
        const downloadRoute = $(this).data('download-route');

        // Set the download button URL
        $('#download-preview-btn').attr('href', downloadRoute);

        // Store the file ID for the sign button
        $('#sign-preview-btn').data('file-id', fileId);

        // Get the row data to determine status
        let rowData = null;
        const allData = datatable.getDataSourceParam('data');

        // Find the row with matching ID
        if (allData && allData.length) {
            rowData = allData.find(item => item.id == fileId);
        }

        // If we couldn't find the row data, try an alternative approach
        if (!rowData) {
            // Try to get the status directly from the DOM
            const statusCell = $(this).closest('tr').find('td:contains("New"),td:contains("Pending Revision")');

            // Only enable the sign button if we found "New" or "Pending Revision" in the row
            if (statusCell.length > 0) {
                console.log('Found New or Pending Revision status in DOM');
                $('#sign-preview-btn').removeClass('disabled').prop('disabled', false);
            } else {
                console.log('Status is not New or Pending Revision');
                $('#sign-preview-btn').addClass('disabled').prop('disabled', true)
                    .attr('title', 'Cannot sign at this stage');
            }
        } else {
            // Enable the sign button if status is "New" or "Pending Revision"
            if (rowData.status === '<?php echo e(ImportFile::STATUS_NEW); ?>' || rowData.status === '<?php echo e(ImportFile::STATUS_PENDING_REVISION); ?>') {
                console.log('Status is New or Pending Revision');
                $('#sign-preview-btn').removeClass('disabled').prop('disabled', false);
            } else {
                console.log('Status is not New:', rowData.status);
                $('#sign-preview-btn').addClass('disabled').prop('disabled', true)
                    .attr('title', 'Cannot sign at this stage');
            }
        }

        // Show the modal
        $('#scriptPreviewModal').modal('show');

        // Load the script preview
        $('#script-preview-content').html(
            '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
        );

        // Load the PDF in an iframe
        setTimeout(function() {
            $('#script-preview-content').html(
                `<iframe src="${viewRoute}" width="100%" height="100%" frameborder="0"></iframe>`
            );
        }, 500);
    });

    // Handle sign button click in the modal
    $('#sign-preview-btn').on('click', function() {
        const fileId = $(this).data('file-id');

        if (!fileId) {
            console.error('No file ID found for signing');
            return;
        }

        // Close the modal
        $('#scriptPreviewModal').modal('hide');

        // Call the captureTimeAndSign function with the file ID
        captureTimeAndSign(fileId);
    });

    // Handle modal events
    $('#scriptPreviewModal').on('hidden.bs.modal', function() {
        // Clear the preview content when modal is closed
        $('#script-preview-content').html('');
    });

    // Add event handlers for filter elements
    $('#script_date_filter, #provider_filter, #medication_filter').on('change', function() {
        // Clear selections when filters change
        globalSelectedIds = [];

        // Get current filter values
        const scriptDateValue = $('#script_date_filter').val();
        const providerId = $('#provider_filter').val();
        const medicationId = $('#medication_filter').val();
        // Get current search value - this will preserve the search text when changing filters
        const searchValue = $(searchElement).val() || '';

        // Format the date properly
        let formattedScriptDate = '';
        if (scriptDateValue) {
            // Ensure it's in YYYY-MM-DD format
            const dateObj = new Date(scriptDateValue);
            if (!isNaN(dateObj.getTime())) {
                formattedScriptDate = dateObj.toISOString().split('T')[0];
            }
        }

        // Set the query parameters for the datatable
        datatable.setDataSourceQuery({
            script_date: formattedScriptDate,
            provider_id: providerId,
            medication_id: medicationId,
            search: searchValue,
            query: {
                script_date: formattedScriptDate,
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue
            }
        });

        // Disable all buttons while loading
        togglePageButtons(true);
        toggleSelectedButtons(true);

        // Reload the datatable with the new query parameters
        datatable.reload();
    });

    // Clear date filter
    $('#clear_date_filter').on('click', function() {
        $('#script_date_filter').val('');

        // Clear selections when filters change
        globalSelectedIds = [];

        // Update the query parameters
        const providerId = $('#provider_filter').val();
        const medicationId = $('#medication_filter').val();
        // Get current search value
        const searchValue = $(searchElement).val() || '';

        // Set the query parameters for the datatable
        datatable.setDataSourceQuery({
            script_date: '',
            provider_id: providerId,
            medication_id: medicationId,
            search: searchValue,
            query: {
                script_date: '',
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue
            }
        });

        // Disable all buttons while loading
        togglePageButtons(true);
        toggleSelectedButtons(true);

        // Reload the datatable
        datatable.reload();
    });

    // Simple row click handler
    $(document).on('click', '#ready_to_sign_dt tbody tr td', function(e) {
        // Skip if clicking on the checkbox cell or actions cell
        if ($(this).is(':first-child') || $(this).is(':last-child') ||
            $(e.target).is('input[type="checkbox"]') ||
            $(e.target).closest('a').length ||
            $(e.target).closest('button').length ||
            $(e.target).closest('i').length) {
            return;
        }

        // Find the checkbox in the first cell
        const checkbox = $(this).closest('tr').find('td:first-child input[type="checkbox"]');

        // Toggle the checkbox
        checkbox.prop('checked', !checkbox.prop('checked'));

        // Trigger change event
        checkbox.trigger('change');
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/scripts/index-ready-to-sign.blade.php ENDPATH**/ ?>