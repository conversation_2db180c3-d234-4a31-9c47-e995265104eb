<?php

namespace App\Http\Livewire;

use App\Events\ScriptStatusChanged;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\Medication;
use App\Models\User;
use App\Models\State;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class NewImport extends Component
{
    public $states = [];
    public $medications = [];
    public ImportFile $importFile;

    public function mount(ImportFile $importFile)
    {
        $this->loadStates();
        $this->loadMedications();

        // Set the import file and load the returnedByUser relationship
        $this->importFile = $importFile;

        // Load the returnedByUser relationship if the importFile has an ID
        if ($this->importFile->id) {
            $this->importFile->load('returnedByUser');
        }

        // Set default script date to today only if it's a new record (no existing script_date)
        if (!$this->importFile->script_date) {
            $this->importFile->script_date = Carbon::today()->format('Y-m-d');
        }
    }

    public function rules()
    {

        return [
            'importFile.script_date' => 'required|date',
            'importFile.last_name' => 'required|string|max:255',
            'importFile.first_name' => 'required|string|max:255',
            'importFile.dob' => 'required|date|before:today',
            'importFile.gender' => 'nullable|string|max:50',
            'importFile.address' => 'required|string|max:255',
            'importFile.city' => 'required|string|max:255',
            'importFile.state' => 'nullable',
            'importFile.zip' => 'required|string|max:20',
            'importFile.phone' => 'nullable|string|max:20',
            'importFile.medication' => 'required|string|max:255',
            'importFile.stregnth' => 'required|string|max:255',
            'importFile.dosing' => 'required|string|max:255',
            'importFile.refills' => 'nullable|string|max:50',
            'importFile.vial_quantity' => 'nullable|string|max:50',
            'importFile.sig' => 'nullable|string|max:255',
            'importFile.notes' => 'nullable|string',
        ];
    }

    public function messages()
    {
        return [
            'importFile.dob.before' => 'The date of birth must be a date before today.',
        ];
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function loadMedications()
    {
        $this->medications = Medication::where('is_active', true)->get();
    }

    public function savePrescription()
    {
        $this->validate();

        $isNew = !$this->importFile->id;
        $action = $isNew ? 'create' : 'update';

        if ($isNew) {
            // Create a new import record
            $import = Import::create([
                'file_name' => 'Manual Entry',
                'user_id' => Auth::id(),
            ]);

            // Set the import_id and other required fields
            $this->importFile->import_id = $import->id;
            $this->importFile->status = ImportFile::STATUS_NEW;
            $this->importFile->number = 1;
        } else {
            $import = $this->importFile->import;
        }

        // Create storage path (DRY improvement)
        $storagePath = 'public/prescriptions/' . $import->id;
        Storage::makeDirectory($storagePath);

        // Save prescription (must be done in both create and update)
        $this->importFile->save();
        $prescription = $this->importFile;

        // Get user and state info
        $user = User::find(Auth::id());
        $userState = null;
        $doctorName = 'Dr. April';

        if ($user) {
            $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
            if ($user->state_id) {
                $userState = State::find($user->state_id);
            }
        }

        // Format dates
        $scriptDateFormatted = $this->importFile->script_date;
        if ($scriptDateFormatted && !is_string($scriptDateFormatted)) {
            try {
                $scriptDateFormatted = Carbon::parse($scriptDateFormatted)->format('m/d/Y');
            } catch (\Exception) {
                // keep original
            }
        }

        $dobFormatted = $this->importFile->dob;
        if ($dobFormatted && !is_string($dobFormatted)) {
            try {
                $dobFormatted = Carbon::parse($dobFormatted)->format('m/d/Y');
            } catch (\Exception) {
                // keep original
            }
        }

        // Prepare PDF data
        $data = [
            $scriptDateFormatted,
            $this->importFile->last_name,
            $this->importFile->first_name,
            $dobFormatted,
            $this->importFile->gender,
            $this->importFile->address,
            $this->importFile->city,
            $this->importFile->state,
            $this->importFile->zip,
            $this->importFile->phone,
            $this->importFile->medication,
            $this->importFile->stregnth,
            $this->importFile->dosing,
            $this->importFile->refills,
            $this->importFile->vial_quantity,
            $this->importFile->sig,
            $this->importFile->notes,
        ];

        $pdf = PDF::loadView('pdf.new-prescription', [
            'data' => $data,
            'isPdfDownload' => true,
            'user' => $user,
            'userState' => $userState,
            'doctorName' => $doctorName,
            'isSigned' => false,
        ]);
        $pdf->setPaper('letter');

        // Save PDF
        $fileName = 'prescription_' . $prescription->id . '.pdf';
        $filePath = $storagePath . '/' . $fileName;
        Storage::put($filePath, $pdf->output());

        // Update record with file info
        $prescription->update([
            'file_name' => $fileName,
            'file_path' => $filePath,
        ]);

        // Create medication if needed
        Medication::firstOrCreate(['name' => $this->importFile->medication]);

        if ($isNew) {
            $this->importFile = new ImportFile();
            $this->importFile->script_date = Carbon::today()->format('Y-m-d');
        }

        session()->flash('message', 'Prescription saved successfully!');

        return $action === 'update'
            ? redirect()->route('scripts.ready-to-sign')
            : redirect()->route('excel.view-pdf', ['id' => $prescription->id]);
    }



    public function saveAndSignPrescription()
    {
        $importFile = $this->importFile;

        if (!$importFile) {
            session()->flash('error-message', 'Prescription not found.');
            return;
        }

        // Delete existing PDF if present
        if ($importFile->file_path && Storage::exists($importFile->file_path)) {
            Storage::delete($importFile->file_path);
        }

        // Fetch associated user
        $user = optional($importFile->import)->user ?? User::find(optional($importFile->import)->user_id);
        $userState = $user?->state_id ? State::find($user->state_id) : null;
        $doctorName = $user?->printed_name ?? ($user?->first_name . ' ' . $user?->last_name ?? 'Dr. April');

        // Format fields
        $data = [
            $importFile->script_date ? Carbon::parse($importFile->script_date)->format('m/d/Y') : '',
            $importFile->last_name,
            $importFile->first_name ?? '',
            $importFile->dob ? Carbon::parse($importFile->dob)->format('m/d/Y') : '',
            $importFile->gender,
            $importFile->address,
            $importFile->city,
            $importFile->state,
            $importFile->zip,
            $importFile->phone,
            $importFile->medication,
            $importFile->stregnth,
            $importFile->dosing,
            $importFile->refills,
            $importFile->vial_quantity,
            $importFile->sig ?? '',
            $importFile->notes ?? '',
        ];

        // Signature image path
        $signatureImagePath = $user?->signature
            ? storage_path('app/public/' . $user->signature)
            : null;

        if ($signatureImagePath && !file_exists($signatureImagePath)) {
            $signatureImagePath = null;
        }

        // Signed timestamp (client > session > fallback)
        $signedAt = $this->resolveSignedAt();

        // Update file status and save
        $importFile->status = ImportFile::STATUS_PENDING_APPROVAL;
        $importFile->signed_at = $signedAt;
        $importFile->save();

        // Dispatch status change event
        if ($currentUser = Auth::user()) {
            event(new ScriptStatusChanged([$importFile->toArray()], $currentUser));

            Log::info('ScriptStatusChanged event dispatched', [
                'user_id' => $currentUser->id,
                'user_name' => $currentUser->first_name . ' ' . $currentUser->last_name,
                'import_file_id' => $importFile->id
            ]);
        }

        // Generate PDF
        $formattedSignedAt = $importFile->signed_at
            ? Carbon::parse($importFile->signed_at)->format('m/d/Y h:i A')
            : now()->format('m/d/Y h:i A');

        Log::info('Generating signed PDF', [
            'import_file_id' => $importFile->id,
            'signed_at_db' => $importFile->signed_at,
            'formatted_signed_at' => $formattedSignedAt,
        ]);

        $pdf = PDF::loadView('pdf.new-prescription', [
            'data' => $data,
            'isPdfDownload' => true,
            'user' => $user,
            'userState' => $userState,
            'doctorName' => $doctorName,
            'isSigned' => true,
            'userSignature' => $signatureImagePath,
            'signed_at' => $formattedSignedAt,
            'ip_address' => request()->ip(),
        ]);

        $pdf->setOption('isPhpEnabled', true)
            ->setOption('isHtml5ParserEnabled', true)
            ->setOption('isRemoteEnabled', false)
            ->setPaper('letter');

        Storage::put($importFile->file_path, $pdf->output());

        return redirect()
            ->route('scripts.ready-to-sign')
            ->with('success-message', 'Scripts signed and sent for approval successfully.');
    }

    private function resolveSignedAt(): Carbon
    {
        $deviceTime = session('device_time');

        if ($deviceTime) {
            try {
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s O', $deviceTime);
                Log::info('Using device time from session for signed_at in archive', [
                    'timestamp' => $deviceTime
                ]);
                return $signedAt;
            } catch (\Exception $e) {
                Log::error('Failed to parse device time from session in archive', [
                    'timestamp' => $deviceTime,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Using current server time for signed_at in archive');
        return Carbon::now();
    }


    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);

        // Emit contentChanged event to re-initialize Select2 after property updates
        $this->emit('contentChanged');
    }

    public function render()
    {
        return view('livewire.new-import');
    }
}
