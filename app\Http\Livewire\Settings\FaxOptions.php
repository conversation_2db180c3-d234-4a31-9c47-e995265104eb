<?php

namespace App\Http\Livewire\Settings;

use App\Models\FaxNumbers;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class FaxOptions extends Component
{
    public $faxNumbers = [];
    public $newFaxNumber = '';
    public $newFaxInputs = []; // Array to store temporary new fax inputs

    protected $rules = [
        'faxNumbers.*.numbers' => 'required|string|min:11|max:12|regex:/^\d{11,12}$/',
        'faxNumbers.*.label' => 'required|string|max:225',
        'newFaxNumber' => 'required|string|min:11|max:12|regex:/^\d{11,12}$/',
        'newFaxInputs.*.number' => 'required|string|min:11|max:12|regex:/^\d{11,12}$/',
        'newFaxInputs.*.label' => 'required|string|max:225',
    ];

    protected $messages = [
        'faxNumbers.*.label.required' => 'Label is required.',
        'faxNumbers.*.label.max' => 'Label cannot exceed 225 characters.',
        'faxNumbers.*.numbers.required' => 'Fax number is required.',
        'faxNumbers.*.numbers.regex' => 'Fax number must be a valid number (11-12 digits only).',
        'faxNumbers.*.numbers.min' => 'Fax number must be at least 11 digits.',
        'faxNumbers.*.numbers.max' => 'Fax number cannot exceed 12 digits.',
        'newFaxInputs.*.label.required' => 'Label is required.',
        'newFaxInputs.*.label.max' => 'Label cannot exceed 225 characters.',
        'newFaxInputs.*.number.required' => 'Fax number is required.',
        'newFaxInputs.*.number.regex' => 'Fax number must be a valid number (11-12 digits only).',
        'newFaxInputs.*.number.min' => 'Fax number must be at least 11 digits.',
        'newFaxInputs.*.number.max' => 'Fax number cannot exceed 12 digits.',
        'newFaxNumber.required' => 'Fax number is required.',
        'newFaxNumber.regex' => 'Fax number must be a valid number (11-12 digits only).',
        'newFaxNumber.min' => 'Fax number must be at least 11 digits.',
        'newFaxNumber.max' => 'Fax number cannot exceed 12 digits.',
    ];

    public function mount()
    {
        $this->loadFaxNumbers();
    }

    public function loadFaxNumbers()
    {
        $this->faxNumbers = FaxNumbers::where('is_active', 1)
            ->get()
            ->map(function ($fax) {
                return [
                    'id' => $fax->id,
                    'numbers' => $fax->numbers,
                    'label' => $fax->label,
                ];
            })
            ->toArray();
    }

    public function addNewFaxNumber()
    {
        // Just add a new empty input field to the temporary inputs array
        $this->newFaxInputs[] = [
            'number' => '',
            'label' => ''
        ];

        $this->emit('alert', ['type' => 'success', 'message' => 'New fax number field added. Fill it in and click Save to store.']);
    }

    public function removeNewFaxInput($index)
    {
        if (isset($this->newFaxInputs[$index])) {
            unset($this->newFaxInputs[$index]);
            // Re-index the array to maintain proper indexing
            $this->newFaxInputs = array_values($this->newFaxInputs);

            $this->emit('alert', ['type' => 'success', 'message' => 'Fax number field removed.']);
        }
    }

    public function deleteFaxNumber($index)
    {
        if (isset($this->faxNumbers[$index])) {
            // Check if the fax number is empty
            if (empty($this->faxNumbers[$index]['numbers'])) {
                $this->emit('alert', ['type' => 'error', 'message' => 'Cannot delete empty fax number. Please enter a valid number or remove the field.']);
                return;
            }

            // If it's an existing record, delete from database
            if (isset($this->faxNumbers[$index]['id']) && $this->faxNumbers[$index]['id']) {
                $faxNumber = FaxNumbers::find($this->faxNumbers[$index]['id']);
                if ($faxNumber) {
                    $faxNumber->delete();
                }
            }

            // Remove from array
            unset($this->faxNumbers[$index]);
            $this->faxNumbers = array_values($this->faxNumbers); // reindex the array

            $this->emit('alert', ['type' => 'success', 'message' => 'Fax number deleted successfully.']);
        }
    }

    public function store()
    {
        try {
            // Reset error bag
            $this->resetErrorBag();

            // Debug logging
            Log::info('FaxOptions store method called', [
                'faxNumbers' => $this->faxNumbers,
                'newFaxInputs' => $this->newFaxInputs
            ]);

            // Custom validation for uniqueness and empty values
            $this->validateFaxNumbers();

            // Validate using Laravel validation rules
            $this->validate();

            $savedCount = 0;

            // Process existing fax numbers
            foreach ($this->faxNumbers as $faxData) {
                if (!empty(trim($faxData['numbers'])) && !empty(trim($faxData['label']))) {
                    if (isset($faxData['id']) && $faxData['id']) {
                        // Update existing record
                        FaxNumbers::where('id', $faxData['id'])
                            ->update([
                                'numbers' => trim($faxData['numbers']),
                                'label' => trim($faxData['label']),
                                'is_active' => 1
                            ]);
                        $savedCount++;
                    } else {
                        // Create new record
                        FaxNumbers::create([
                            'numbers' => trim($faxData['numbers']),
                            'label' => trim($faxData['label']),
                            'is_active' => 1
                        ]);
                        $savedCount++;
                    }
                }
            }

            // Process new fax inputs
            foreach ($this->newFaxInputs as $newFaxData) {
                if (!empty(trim($newFaxData['number'])) && !empty(trim($newFaxData['label']))) {
                    FaxNumbers::create([
                        'numbers' => trim($newFaxData['number']),
                        'label' => trim($newFaxData['label']),
                        'is_active' => 1
                    ]);
                    $savedCount++;
                }
            }

            // Clear the temporary inputs after saving
            $this->newFaxInputs = [];

            $this->emit('alert', ['type' => 'success', 'message' => "Fax numbers saved successfully. {$savedCount} fax number(s) processed."]);
            $this->loadFaxNumbers(); // Reload to get fresh data with IDs

        } catch (ValidationException $e) {
            // Validation errors are already handled by Livewire
            $this->emit('alert', ['type' => 'error', 'message' => 'Please fix the validation errors and try again.']);
        } catch (\Exception $e) {
            Log::error('Error saving fax numbers: ' . $e->getMessage(), [
                'faxNumbers' => $this->faxNumbers,
                'newFaxInputs' => $this->newFaxInputs,
                'trace' => $e->getTraceAsString()
            ]);
            $this->emit('alert', ['type' => 'error', 'message' => 'Error saving fax numbers: ' . $e->getMessage()]);
        }
    }

    private function validateFaxNumbers()
    {
        $numbers = [];
        $errors = [];

        // Validate existing fax numbers
        foreach ($this->faxNumbers as $index => $faxData) {
            $number = trim($faxData['numbers'] ?? '');

            // Check for empty numbers
            if (empty($number)) {
                $errors["faxNumbers.{$index}.numbers"] = 'Fax number cannot be empty.';
                continue;
            }

            // Check for duplicates
            if (in_array($number, $numbers)) {
                $errors["faxNumbers.{$index}.numbers"] = 'This fax number is duplicated.';
            } else {
                $numbers[] = $number;
            }
        }

        // Validate new fax inputs
        foreach ($this->newFaxInputs as $index => $newFaxData) {
            $number = trim($newFaxData['number'] ?? '');

            // Check for empty numbers
            if (empty($number)) {
                $errors["newFaxInputs.{$index}.number"] = 'Fax number cannot be empty.';
                continue;
            }

            // Check for duplicates with existing numbers and other new inputs
            if (in_array($number, $numbers)) {
                $errors["newFaxInputs.{$index}.number"] = 'This fax number is duplicated.';
            } else {
                $numbers[] = $number;
            }
        }

        if (!empty($errors)) {
            foreach ($errors as $field => $message) {
                $this->addError($field, $message);
            }
            throw new ValidationException(validator([], []));
        }
    }

    public function updated($propertyName)
    {
        // Check for real-time duplicate validation when editing existing numbers
        if (strpos($propertyName, 'faxNumbers.') === 0 && strpos($propertyName, '.numbers') !== false) {
            $this->validateFaxNumberUniqueness($propertyName);
        }

        // Check for real-time duplicate validation when editing new fax inputs
        if (strpos($propertyName, 'newFaxInputs.') === 0 && strpos($propertyName, '.number') !== false) {
            $this->validateNewFaxInputUniqueness($propertyName);
        }

        $this->validateOnly($propertyName);

        // Emit contentChanged event to re-initialize Select2 after property updates
        $this->emit('contentChanged');
    }

    private function validateFaxNumberUniqueness($propertyName)
    {
        // Extract index from property name (e.g., "faxNumbers.0.numbers" -> 0)
        preg_match('/faxNumbers\.(\d+)\.numbers/', $propertyName, $matches);
        if (!isset($matches[1])) return;

        $currentIndex = (int)$matches[1];
        $currentValue = trim($this->faxNumbers[$currentIndex]['numbers'] ?? '');

        if (empty($currentValue)) return;

        // Check if this number exists in other entries
        foreach ($this->faxNumbers as $index => $faxData) {
            if ($index !== $currentIndex && trim($faxData['numbers'] ?? '') === $currentValue) {
                $this->addError($propertyName, 'This fax number already exists.');
                return;
            }
        }

        // Clear any existing error for this field if validation passes
        $this->resetErrorBag($propertyName);
    }

    private function validateNewFaxInputUniqueness($propertyName)
    {
        // Extract index from property name (e.g., "newFaxInputs.0.number" -> 0)
        preg_match('/newFaxInputs\.(\d+)\.number/', $propertyName, $matches);
        if (!isset($matches[1])) return;

        $currentIndex = (int)$matches[1];
        $currentValue = trim($this->newFaxInputs[$currentIndex]['number'] ?? '');

        if (empty($currentValue)) return;

        // Check if this number exists in existing fax numbers
        foreach ($this->faxNumbers as $faxData) {
            if (trim($faxData['numbers'] ?? '') === $currentValue) {
                $this->addError($propertyName, 'This fax number already exists.');
                return;
            }
        }

        // Check if this number exists in other new inputs
        foreach ($this->newFaxInputs as $index => $newFaxData) {
            if ($index !== $currentIndex && trim($newFaxData['number'] ?? '') === $currentValue) {
                $this->addError($propertyName, 'This fax number is duplicated.');
                return;
            }
        }

        // Clear any existing error for this field if validation passes
        $this->resetErrorBag($propertyName);
    }

    public function render()
    {
        return view('livewire.settings.fax-options');
    }
}
