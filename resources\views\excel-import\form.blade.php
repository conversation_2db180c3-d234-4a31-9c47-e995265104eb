@extends('master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    {{-- <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Excel Import</h5>
                                <a href="{{ url()->previous() }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-arrow-left mr-1"></i> Back
                                </a>
                            </div> --}}

                    <div class="card-body">
                        @if (session('status'))
                            <div class="alert alert-success" role="alert">
                                {{ session('status') }}
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert alert-danger" role="alert">
                                {{ session('error') }}
                            </div>
                        @endif

                        <!-- Progress Steps -->
                        <div class="progress-steps-container mb-5">
                            <div class="progress-line">
                                <div class="progress-line-inner"></div>
                            </div>
                            <div class="progress-steps">
                                <div class="progress-step active">
                                    <div class="step-circle">1</div>
                                    <div class="step-label">Upload</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-circle">2</div>
                                    <div class="step-label">Create</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-circle">3</div>
                                    <div class="step-label">Sign</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-circle">4</div>
                                    <div class="step-label">Done</div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <h6 class="mb-3">Select Excel File</h6>
                                <form action="{{ route('excel.store') }}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    <div class="form-group">
                                        <div class="custom-file">
                                            <input type="file" name="excel_file"
                                                class="custom-file-input @error('excel_file') is-invalid @enderror"
                                                id="excel_file" accept=".xlsx,.xls" style="opacity: 0; position: absolute;">
                                            <div class="d-flex">
                                                <input type="text" class="form-control" id="file_name_display"
                                                    placeholder="No file selected" readonly>
                                                <button type="button" class="btn btn-secondary"
                                                    onclick="document.getElementById('excel_file').click()">Browse</button>
                                            </div>
                                            @error('excel_file')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                        <small class="form-text text-muted">Upload an Excel file (.xlsx or
                                            .xls)</small>
                                    </div>
                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-danger px-4" id="import-btn">Import</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="text-end me-3">
                            <a href="/sample_files/Excel-template.xlsx" download="Excel-template.xlsx" class="download-template">Download
                                Excel
                                template</a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .download-template {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #6b21a8;
            font-weight: 500;
            text-decoration: underline;
            font-size: 18px;
            /* Increase this if you want it even bigger */
        }



        .card {
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            border-radius: 0;
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #f1f1f1;
            padding: 15px 20px;
        }

        /* Progress Steps Styling */
        .progress-steps-container {
            position: relative;
            padding: 20px 0;
            margin: 0 auto;
            max-width: 700px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .progress-step {
            text-align: center;
            width: 20%;
            position: relative;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .progress-step.active .step-circle {
            background-color: #000000;
            color: white;
        }

        .step-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 400;
        }

        .progress-step.active .step-label {
            color: #000000;
            font-weight: 500;
        }

        .progress-line {
            position: absolute;
            top: 38px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 0;
        }

        .progress-line-inner {
            height: 100%;
            width: 20%;
            background-color: #000000;
        }

        .btn-danger {
            background-color: #000000;
            border-color: #000000;
        }

        .custom-file-label {
            border-radius: 0;
        }

        .custom-file-label::after {
            background-color: #f8f9fa;
            color: #212529;
            border-radius: 0;
        }

        /* Custom file input styling */
        .form-control {
            border-radius: 0;
            border-right: none;
            a
        }

        .btn-secondary {
            background-color: #f8f9fa;
            color: #212529;
            border-color: #ced4da;
            border-radius: 0;
            border-left: none;
        }
    </style>
@endsection

@push('scripts')
    <script>
        // Store the Excel import form route for targeting
        var excelImportFormRoute = "{{ route('excel.store') }}";

        // Display file name when selected
        document.getElementById('excel_file').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                // Get just the filename without any path
                var fileName = e.target.files[0].name;

                // Update the text input with just the filename
                document.getElementById('file_name_display').value = fileName;

                // Enable the submit button - specifically target the Excel import form
                var excelForm = document.querySelector('form[action="' + excelImportFormRoute + '"]');
                if (excelForm) {
                    var submitButton = excelForm.querySelector('#import-btn');
                    if (submitButton) {
                        submitButton.disabled = false;
                    }
                }
            }
        });

        // Form validation - specifically target the Excel import form
        document.addEventListener('DOMContentLoaded', function() {
            // Find the Excel import form
            var excelForm = document.querySelector('form[action="' + excelImportFormRoute + '"]');

            if (excelForm) {
                // Add submit event listener only to the Excel import form
                excelForm.addEventListener('submit', function(e) {
                    var fileInput = document.getElementById('excel_file');
                    if (fileInput.files.length === 0) {
                        e.preventDefault();
                        alert('Please select an Excel file to import');
                        return false;
                    }

                    // Show loading state - specifically target the import button by ID
                    var submitButton = this.querySelector('#import-btn');
                    if (submitButton) {
                        submitButton.innerHTML =
                            '<span class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span> Importing...';
                        submitButton.disabled = true;
                    }
                });

                // Initially disable the submit button until a file is selected
                var fileInput = document.getElementById('excel_file');
                var submitButton = excelForm.querySelector('#import-btn');

                if (fileInput && submitButton && fileInput.files.length === 0) {
                    submitButton.disabled = true;
                }
            }
        });
    </script>
@endpush
